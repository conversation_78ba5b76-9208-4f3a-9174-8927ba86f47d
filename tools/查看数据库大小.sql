-- 数据库的大小，大于 0.5g
select *
from (SELECT table_schema                                         AS 'Database Name',
             SUM(data_length + index_length) / 1024 / 1024 / 1024 AS dbsize
      FROM information_schema.TABLES
      GROUP BY table_schema) as t
where dbsize > 0.5;


-- 数据库下每个表的大小
SELECT
    table_name,
    table_rows,
    round(data_length / 1024 / 1024 / 1024, 2) AS data_size_gb,
    round(index_length / 1024 / 1024 / 1024, 2) AS index_size_gb
FROM
    information_schema.TABLES
WHERE
    table_schema = 'qa_info'
ORDER BY
    data_length DESC, index_length DESC;



SELECT
    table_name,
    table_rows,
    round(data_length / 1024 / 1024 / 1024, 2) AS data_size_gb,
    round(index_length / 1024 / 1024 / 1024, 2) AS index_size_gb
FROM
    information_schema.TABLES
WHERE
    table_schema = 'qa_info'
ORDER BY
    data_length DESC, index_length DESC;
