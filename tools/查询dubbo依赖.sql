-- 最保守的优化方案
SELECT DISTINCT
    t1.service_name as target_service,
    t1.type target_type,
    t2.service_name as related_service,
    t2.type related_type,
    t1.zk_ip,
    t1.api_path
FROM (
    SELECT DISTINCT type, service_name, zk_ip, api_path
    FROM scan_zk_dubbo_log_info
    WHERE create_time > '2025-06-03'
        AND service_name IN ('howbuy-cms-server','howbuy-content-server','howbuy-fund-server','howbuy-member-remote','howbuy-simu-server','howbuy-web-server')
) t1,
(
    SELECT DISTINCT type, service_name, zk_ip, api_path
    FROM scan_zk_dubbo_log_info
    WHERE create_time > '2025-06-03'
) t2
WHERE t1.zk_ip = t2.zk_ip
    AND t1.api_path = t2.api_path
    AND t1.type != t2.type
    AND t1.service_name != t2.service_name
    ORDER BY target_service,related_service,api_path;