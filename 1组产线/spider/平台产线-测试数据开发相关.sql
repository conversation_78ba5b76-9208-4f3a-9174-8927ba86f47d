select * from db_mgt_cdc_info;
select * from db_mgt_cdc_record_info;
select * from db_mgt_bis_test_sql where bis_pipeline_id="FIN-CONSOLE_20240314-fin-linna" order by id desc;
select * from db_mgt_bis_test_sql_commit_his order by id desc;
select * from db_mgt_bis_test_sql_archive_his order by id desc;
select * from db_mgt_db_restore_his where id =318;

select * from task_mgt_service_results where business_name = "archive" and script_params like "%otc_4.0.58%" order by id desc;
select distinct business_name from task_mgt_service_results  order by id desc;

select * from db_mgt_sql where sql_ver_name ='V2024.***********.31.001__001_split_1.trade_ddl.sql';

select * from env_mgt_node where node_ip='***************';

select resore_his.db_name, cdc_info.* from db_mgt_cdc_info cdc_info join db_mgt_db_restore_his resore_his on cdc_info.restore_his_id=resore_his.id;


 SELECT h.db_name as suite_db_name, MAX(c.create_time) as create_time,MAX(c.cdc_pipeline_url) as cdc_pipeline_url
FROM db_mgt_cdc_record_info c
JOIN db_mgt_db_restore_his h ON c.restore_his_id = h.id
JOIN db_mgt_cdc_info cdc_info ON cdc_info.restore_his_id = h.id
WHERE h.opt_pipeline_id = 'TRADE-BATCH-ONLINE_3.22.1'
GROUP BY h.db_name;


create table spider.db_mgt_bis_test_sql_archive_his
(
    id                  bigint auto_increment
    primary key,
    bis_pipeline_id     varchar(100) null comment '业务数据生产迭代id',
    db_info_id     bigint(11) null comment '数据库 ID',
    arc_sql_ver_name        varchar(255) null comment '归档sql版本化名',
    gitlab_repo_version varchar(255) null,
    git_archive_time     datetime     null comment '归档时间',
    operate_user        varchar(50)  null comment '归档人',
    constraint udx_module_iter_file
        unique (bis_pipeline_id, db_info_id,arc_sql_ver_name)
)
    comment 'test_dml归档历史表';



select * from db_mgt_sql where sql_ver_name='V2024.***********.05.001__001_split_1.alter_t_coop_merchant_info.sql';
select * from db_mgt_sql where sql_ver_name like '%split_7_alter_T_FIN_SETTLE_DAYS_CONFIG.sql%' and git_group='tradenew' order by ;
select * from db_mgt_sql where  br_name='20240129'