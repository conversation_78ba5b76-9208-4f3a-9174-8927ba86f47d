select distinct  nb.module_name as appName  ,n.node_ip as clientIp ,s.suite_code as suite<PERSON><PERSON>,CONCAT(r.addr_name,r.type_name) as groupName
from env_mgt_node_bind nb
         left join env_mgt_suite s on nb.suite_id = s.id
         left join env_mgt_region r on s.region_id = r.id
         left join env_mgt_deploy_group g on nb.deploy_group = g.id
         left join env_mgt_node n on n.id = nb.node_id
         left join product_mgt_product_info p ON nb.lib_repo_info_id=p.id
         left join iter_mgt_iter_info i ON i.pipeline_id = p.iteration_id
         left join app_mgt_app_module amam on nb.module_name = amam.module_name
         left join team_mgt_app_bind tmab on tmab.app_id = amam.id
         left join team_mgt_user_bind tmub on tmab.team_id = tmub.team_id
         join app_mgt_app_info ai on ai.id = amam.app_id
where r.region_group = 'prod'
  and n.node_status = '0';


select amam.module_name as appName,group_concat(concat(auser.last_name,auser.first_name) ORDER BY auser.last_name, auser.first_name) as userNames from app_mgt_app_info  amai
         join app_mgt_app_module amam on amai.id = amam.app_id
         join team_mgt_app_bind tmab on amai.id = tmab.app_id
         join team_mgt_user_bind tmub on tmab.team_id = tmub.team_id
         join auth_user auser on auser.username = tmub.user_name
where tmub.bind_is_active=1
and amam.need_online=1
group by amam.module_name;


SELECT
		   producer.appName AS '生产者应用名称',
    producer.topicName AS '主题名称',
    consumer.appName AS '消费者应用名称'
FROM
    rmq_migrate_pqc AS producer
LEFT JOIN
    rmq_migrate_pqc AS consumer
ON
    producer.topicName = consumer.topicName
    AND consumer.relationShip = 'consumer'
WHERE
    producer.relationShip = 'producer'
    AND producer.topicName IN (
        SELECT
            topicName
        FROM
            rmq_migrate_pqc
        WHERE
            relationShip = 'producer'
        GROUP BY
            topicName
        HAVING
            COUNT(*) = 1
    );


# 查询业务集群主题关系 1生产者
SELECT
    producer.appName AS '生产者应用名称',
        producer_responsible.userNames AS '生产者负责人',
    producer.topicName AS '主题名称',
    consumer.appName  AS '消费者应用名称',
    consumer_responsible.userNames AS '消费者负责人',
    consumer.messageModel AS  '消费模式'
FROM
    rmq_migrate_pqc AS producer
LEFT JOIN
    rmq_migrate_pqc AS consumer
ON
    producer.topicName = consumer.topicName
    AND consumer.relationShip = 'consumer'
LEFT JOIN (
    SELECT
        amam.module_name AS appName,
        GROUP_CONCAT(CONCAT(auser.last_name, auser.first_name) ORDER BY auser.last_name, auser.first_name SEPARATOR ', ') AS userNames
    FROM
        app_mgt_app_info amai
    JOIN
        app_mgt_app_module amam ON amai.id = amam.app_id
    JOIN
        team_mgt_app_bind tmab ON amai.id = tmab.app_id
    JOIN
        team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
    JOIN
        auth_user auser ON auser.username = tmub.user_name
    WHERE
        tmub.bind_is_active = 1
        AND amam.need_online = 1
    GROUP BY
        amam.module_name
) AS producer_responsible
ON
    producer.appName = producer_responsible.appName
LEFT JOIN (
    SELECT
        amam.module_name AS appName,
        GROUP_CONCAT(CONCAT(auser.last_name, auser.first_name) ORDER BY auser.last_name, auser.first_name SEPARATOR ', ') AS userNames
    FROM
        app_mgt_app_info amai
    JOIN
        app_mgt_app_module amam ON amai.id = amam.app_id
    JOIN
        team_mgt_app_bind tmab ON amai.id = tmab.app_id
    JOIN
        team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
    JOIN
        auth_user auser ON auser.username = tmub.user_name
    WHERE
        tmub.bind_is_active = 1
        AND amam.need_online = 1
    GROUP BY
        amam.module_name
) AS consumer_responsible
ON
    consumer.appName = consumer_responsible.appName
WHERE
    producer.relationShip = 'producer'
    AND producer.topicName IN (
        SELECT
            topicName
        FROM
            rmq_migrate_pqc
        WHERE
            relationShip = 'producer'
        GROUP BY
            topicName
        HAVING
            COUNT(*) =1
    )
    and (producer_responsible.userNames like "%韩春生%" or producer_responsible.userNames like "%钱科晨%")
	ORDER BY producer_responsible.userNames,producer.appName,producer.topicName;

# 查询业务集群主题关系 多生产者
SELECT distinct
    producer.appName AS '生产者应用名称',
        producer_responsible.userNames AS '生产者负责人',
    producer.topicName AS '主题名称',
    consumer.appName  AS '消费者应用名称',

    consumer_responsible.userNames AS '消费者负责人',
    consumer.messageModel AS  '消费模式'
FROM
    rmq_migrate_pqc AS producer
LEFT JOIN
    rmq_migrate_pqc AS consumer
ON
    producer.topicName = consumer.topicName
    AND consumer.relationShip = 'consumer'
LEFT JOIN (
    SELECT
        amam.module_name AS appName,
        GROUP_CONCAT(CONCAT(auser.last_name, auser.first_name) ORDER BY auser.last_name, auser.first_name SEPARATOR ', ') AS userNames
    FROM
        app_mgt_app_info amai
    JOIN
        app_mgt_app_module amam ON amai.id = amam.app_id
    JOIN
        team_mgt_app_bind tmab ON amai.id = tmab.app_id
    JOIN
        team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
    JOIN
        auth_user auser ON auser.username = tmub.user_name
    WHERE
        tmub.bind_is_active = 1
        AND amam.need_online = 1
    GROUP BY
        amam.module_name
) AS producer_responsible
ON
    producer.appName = producer_responsible.appName
LEFT JOIN (
    SELECT
        amam.module_name AS appName,
        GROUP_CONCAT(CONCAT(auser.last_name, auser.first_name) ORDER BY auser.last_name, auser.first_name SEPARATOR ', ') AS userNames
    FROM
        app_mgt_app_info amai
    JOIN
        app_mgt_app_module amam ON amai.id = amam.app_id
    JOIN
        team_mgt_app_bind tmab ON amai.id = tmab.app_id
    JOIN
        team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
    JOIN
        auth_user auser ON auser.username = tmub.user_name
    WHERE
        tmub.bind_is_active = 1
        AND amam.need_online = 1
    GROUP BY
        amam.module_name
) AS consumer_responsible
ON
    consumer.appName = consumer_responsible.appName
WHERE
    producer.relationShip = 'producer'
    AND producer.topicName IN (
        SELECT
            topicName
        FROM
            rmq_migrate_pqc
        WHERE
            relationShip = 'producer'
        GROUP BY
            topicName
        HAVING
            COUNT(*) >1
    )
    and (producer_responsible.userNames like "%韩春生%" or producer_responsible.userNames like "%钱科晨%")
	ORDER BY producer.topicName,producer_responsible.userNames,producer.appName;

# 没找到生产者的主题
SELECT distinct
    "" AS '生产者应用名称',
        "" AS '生产者负责人',
    consumer.topicName AS '主题名称',
    consumer.appName  AS '消费者应用名称',
    consumer_responsible.userNames AS '消费者负责人',
    consumer.messageModel AS  '消费模式'
FROM rmq_migrate_pqc consumer
LEFT JOIN (
    SELECT
        amam.module_name AS appName,
        GROUP_CONCAT(CONCAT(auser.last_name, auser.first_name) ORDER BY auser.last_name, auser.first_name SEPARATOR ', ') AS userNames
    FROM
        app_mgt_app_info amai
    JOIN
        app_mgt_app_module amam ON amai.id = amam.app_id
    JOIN
        team_mgt_app_bind tmab ON amai.id = tmab.app_id
    JOIN
        team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
    JOIN
        auth_user auser ON auser.username = tmub.user_name
    WHERE
        tmub.bind_is_active = 1
        AND amam.need_online = 1
    GROUP BY
        amam.module_name
) AS consumer_responsible
ON
    consumer.appName = consumer_responsible.appName
where consumer.relationShip = "consumer"
  and consumer.topicName not in (select distinct topicName
                        from rmq_migrate_pqc
                        where relationShip = "producer"
                      )
 and (consumer_responsible.userNames like "%韩春生%" or consumer_responsible.userNames like "%钱科晨%")
order by  consumer.appName,consumer.topicName,consumer_responsible.userNames;




select consumer_responsible.userNames, rmzatr.name,rmzatr.`key` from rmq_migrate_zeus_app_topic_rel rmzatr
LEFT JOIN (
    SELECT
        amam.module_name AS appName,
        GROUP_CONCAT(CONCAT(auser.last_name, auser.first_name) ORDER BY auser.last_name, auser.first_name SEPARATOR ', ') AS userNames
    FROM
        app_mgt_app_info amai
    JOIN
        app_mgt_app_module amam ON amai.id = amam.app_id
    JOIN
        team_mgt_app_bind tmab ON amai.id = tmab.app_id
    JOIN
        team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
    JOIN
        auth_user auser ON auser.username = tmub.user_name
    WHERE
        tmub.bind_is_active = 1
        AND amam.need_online = 1
    GROUP BY
        amam.module_name
) AS consumer_responsible
ON
    rmzatr.name = consumer_responsible.appName
order by consumer_responsible.userNames, rmzatr.name;




select distinct topicName from rmq_migrate_pqc where messageModel="BROADCASTING" order by topicName;

SELECT
    producer.appName AS '生产者应用名称',
        producer_responsible.userNames AS '生产者负责人',
    producer.topicName AS '主题名称',
    consumer.appName  AS '消费者应用名称',

    consumer_responsible.userNames AS '消费者负责人'
FROM
    rmq_migrate_pqc_canal AS producer
LEFT JOIN
    rmq_migrate_pqc_canal AS consumer
ON
    producer.topicName = consumer.topicName
    AND consumer.relationShip = 'consumer'
LEFT JOIN (
    SELECT
        amam.module_name AS appName,
        GROUP_CONCAT(CONCAT(auser.last_name, auser.first_name) ORDER BY auser.last_name, auser.first_name SEPARATOR ', ') AS userNames
    FROM
        app_mgt_app_info amai
    JOIN
        app_mgt_app_module amam ON amai.id = amam.app_id
    JOIN
        team_mgt_app_bind tmab ON amai.id = tmab.app_id
    JOIN
        team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
    JOIN
        auth_user auser ON auser.username = tmub.user_name
    WHERE
        tmub.bind_is_active = 1
        AND amam.need_online = 1
    GROUP BY
        amam.module_name
) AS producer_responsible
ON
    producer.appName = producer_responsible.appName
LEFT JOIN (
    SELECT
        amam.module_name AS appName,
        GROUP_CONCAT(CONCAT(auser.last_name, auser.first_name) ORDER BY auser.last_name, auser.first_name SEPARATOR ', ') AS userNames
    FROM
        app_mgt_app_info amai
    JOIN
        app_mgt_app_module amam ON amai.id = amam.app_id
    JOIN
        team_mgt_app_bind tmab ON amai.id = tmab.app_id
    JOIN
        team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
    JOIN
        auth_user auser ON auser.username = tmub.user_name
    WHERE
        tmub.bind_is_active = 1
        AND amam.need_online = 1
    GROUP BY
        amam.module_name
) AS consumer_responsible
ON
    consumer.appName = consumer_responsible.appName
WHERE
    producer.relationShip = 'producer'
    AND producer.topicName IN (
        SELECT
            topicName
        FROM
            rmq_migrate_pqc_canal
        WHERE
            relationShip = 'producer'
        GROUP BY
            topicName
        HAVING
            COUNT(*) > 1
    )
	ORDER BY producer.topicName;

SELECT
		   producer.appName AS '生产者应用名称',
    producer.topicName AS '主题名称',
    consumer.appName AS '消费者应用名称'
FROM
    rmq_migrate_pqc_canal AS producer
LEFT JOIN
    rmq_migrate_pqc_canal AS consumer
ON
    producer.topicName = consumer.topicName
    AND consumer.relationShip = 'consumer'
WHERE
    producer.relationShip = 'producer'
    AND producer.topicName IN (
        SELECT
            topicName
        FROM
            rmq_migrate_pqc_canal
        WHERE
            relationShip = 'producer'
        GROUP BY
            topicName
        HAVING
            COUNT(*) = 1
    );

select temp.producer AS '生产者应用名称','代景阳' as '生产者负责人', temp.topicName AS '主题名称',temp.consumer AS '消费者应用名称',consumer_responsible.userNames AS '消费者负责人' from (

    SELECT
		   producer.appName AS producer,
    producer.topicName AS topicName,
    consumer.appName AS consumer
FROM
    rmq_migrate_pqc_canal AS producer
LEFT JOIN
    rmq_migrate_pqc_canal AS consumer
ON
    producer.topicName = consumer.topicName
    AND consumer.relationShip = 'consumer'
WHERE
    producer.relationShip = 'producer'
    AND producer.topicName IN (
        SELECT
            topicName
        FROM
            rmq_migrate_pqc_canal
        WHERE
            relationShip = 'producer'
        GROUP BY
            topicName
        HAVING
            COUNT(*) = 1
    )
              ) temp


LEFT JOIN (
    SELECT
        amam.module_name AS appName,
        GROUP_CONCAT(CONCAT(auser.last_name, auser.first_name) ORDER BY auser.last_name, auser.first_name SEPARATOR ', ') AS userNames
    FROM
        app_mgt_app_info amai
    JOIN
        app_mgt_app_module amam ON amai.id = amam.app_id
    JOIN
        team_mgt_app_bind tmab ON amai.id = tmab.app_id
    JOIN
        team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
    JOIN
        auth_user auser ON auser.username = tmub.user_name
    WHERE
        tmub.bind_is_active = 1
        AND amam.need_online = 1
    GROUP BY
        amam.module_name
) AS consumer_responsible
ON
    temp.consumer = consumer_responsible.appName;

select distinct topicName from rmq_migrate_pqc
order by topicName;
select * from rmq_migrate_pqc
where topicName like "%fpc_flushDSTopic%";


# 查询业务集群主题关系 1生产者
select distinct topicRels.* from (

SELECT
    producer.appName AS '生产者应用名称',
    producer_responsible.userNames AS '生产者负责人',
    producer.topicName AS '主题名称',
    consumer.appName  AS '消费者应用名称',
    consumer_responsible.userNames AS '消费者负责人',
    consumer.messageModel AS  '消费模式',
    '宙斯' as '配置在哪'
FROM
    rmq_migrate_pqc_canal AS producer
LEFT JOIN
    rmq_migrate_pqc_canal AS consumer
ON
    producer.topicName = consumer.topicName
    AND consumer.relationShip = 'consumer'
LEFT JOIN (
    SELECT
        amam.module_name AS appName,
        GROUP_CONCAT(CONCAT(auser.last_name, auser.first_name) ORDER BY auser.last_name, auser.first_name SEPARATOR ', ') AS userNames
    FROM
        app_mgt_app_info amai
    JOIN
        app_mgt_app_module amam ON amai.id = amam.app_id
    JOIN
        team_mgt_app_bind tmab ON amai.id = tmab.app_id
    JOIN
        team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
    JOIN
        auth_user auser ON auser.username = tmub.user_name
    WHERE
        tmub.bind_is_active = 1
        AND amam.need_online = 1
    GROUP BY
        amam.module_name
) AS producer_responsible
ON
    producer.appName = producer_responsible.appName
LEFT JOIN (
    SELECT
        amam.module_name AS appName,
        GROUP_CONCAT(CONCAT(auser.last_name, auser.first_name) ORDER BY auser.last_name, auser.first_name SEPARATOR ', ') AS userNames
    FROM
        app_mgt_app_info amai
    JOIN
        app_mgt_app_module amam ON amai.id = amam.app_id
    JOIN
        team_mgt_app_bind tmab ON amai.id = tmab.app_id
    JOIN
        team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
    JOIN
        auth_user auser ON auser.username = tmub.user_name
    WHERE
        tmub.bind_is_active = 1
        AND amam.need_online = 1
    GROUP BY
        amam.module_name
) AS consumer_responsible
ON
    consumer.appName = consumer_responsible.appName
WHERE
    producer.relationShip = 'producer'
    AND producer.topicName IN (
        SELECT
            topicName
        FROM
            rmq_migrate_pqc_canal
        WHERE
            relationShip = 'producer'
        GROUP BY
            topicName
        HAVING
            COUNT(*) =1
    )

    and producer.topicName in ('app_ams_log',
'fpc-slave-db_log',
'fpc_app_ams-master-manage_data_gdsxys',
'fpc_fund-master-manage_data',
'fpc_high-master-manage_data',
'fpc_st_fund-slave',
'fpc_st_hedge-slave',
'fpc_st_main-master-data_ants_broadcast_ddl',
'fpc_st_main-master-data_ants_broadcast_dml',
'fpc_st_main-slave',
'fpc_st_market-slave',
'fpc_st_portfolio-slave',
'FPC_SYNC_FI_AC_FINA_1',
'FPC_SYNC_FI_AC_REPTILE_1',
'FPC_SYNC_FI_ST_MAIN_1',
'FPC_SYNC_FI_VA_FUND_1')

union all

SELECT
    producer.appName AS '生产者应用名称',
    producer_responsible.userNames AS '生产者负责人',
    producer.topicName AS '主题名称',
    consumer.appName  AS '消费者应用名称',
    consumer_responsible.userNames AS '消费者负责人',
    consumer.messageModel AS  '消费模式',
    '应用配置｜DB' as '配置在哪'
FROM
    rmq_migrate_pqc_canal AS producer
LEFT JOIN
    rmq_migrate_pqc_canal AS consumer
ON
    producer.topicName = consumer.topicName
    AND consumer.relationShip = 'consumer'
LEFT JOIN (
    SELECT
        amam.module_name AS appName,
        GROUP_CONCAT(CONCAT(auser.last_name, auser.first_name) ORDER BY auser.last_name, auser.first_name SEPARATOR ', ') AS userNames
    FROM
        app_mgt_app_info amai
    JOIN
        app_mgt_app_module amam ON amai.id = amam.app_id
    JOIN
        team_mgt_app_bind tmab ON amai.id = tmab.app_id
    JOIN
        team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
    JOIN
        auth_user auser ON auser.username = tmub.user_name
    WHERE
        tmub.bind_is_active = 1
        AND amam.need_online = 1
    GROUP BY
        amam.module_name
) AS producer_responsible
ON
    producer.appName = producer_responsible.appName
LEFT JOIN (
    SELECT
        amam.module_name AS appName,
        GROUP_CONCAT(CONCAT(auser.last_name, auser.first_name) ORDER BY auser.last_name, auser.first_name SEPARATOR ', ') AS userNames
    FROM
        app_mgt_app_info amai
    JOIN
        app_mgt_app_module amam ON amai.id = amam.app_id
    JOIN
        team_mgt_app_bind tmab ON amai.id = tmab.app_id
    JOIN
        team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
    JOIN
        auth_user auser ON auser.username = tmub.user_name
    WHERE
        tmub.bind_is_active = 1
        AND amam.need_online = 1
    GROUP BY
        amam.module_name
) AS consumer_responsible
ON
    consumer.appName = consumer_responsible.appName
WHERE
    producer.relationShip = 'producer'
    AND producer.topicName IN (
        SELECT
            topicName
        FROM
            rmq_migrate_pqc_canal
        WHERE
            relationShip = 'producer'
        GROUP BY
            topicName
        HAVING
            COUNT(*) =1
    )

    and producer.topicName  in ('app_ams_log',
'fpc-slave-db_log',
'fpc_app_ams-master-manage_data_gdsxys',
'fpc_fund-master-manage_data',
'fpc_high-master-manage_data',
'fpc_st_fund-slave',
'fpc_st_hedge-slave',
'fpc_st_main-master-data_ants_broadcast_ddl',
'fpc_st_main-master-data_ants_broadcast_dml',
'fpc_st_main-slave',
'fpc_st_market-slave',
'fpc_st_portfolio-slave',
'FPC_SYNC_FI_AC_FINA_1',
'FPC_SYNC_FI_AC_REPTILE_1',
'FPC_SYNC_FI_ST_MAIN_1',
'FPC_SYNC_FI_VA_FUND_1')
) topicRels  order by  '消费者应用名称';





              SELECT distinct producer.appName AS '生产者应用名称',
            producer_responsible.userNames AS '生产者负责人',
            producer.topicName AS '主题名称',
            consumer.appName AS '消费者应用名称',
            consumer_responsible.userNames AS '消费者负责人',
            consumer.messageModel AS '消费模式',
            '宙斯' as '配置在哪'
        FROM rmq_migrate_pqc_canal AS producer
            LEFT JOIN rmq_migrate_pqc_canal AS consumer ON producer.topicName = consumer.topicName
            AND consumer.relationShip = 'consumer'
            LEFT JOIN (
                SELECT amam.module_name AS appName,
                    GROUP_CONCAT(
                        CONCAT(auser.last_name, auser.first_name)
                        ORDER BY auser.last_name,
                            auser.first_name SEPARATOR ', '
                    ) AS userNames
                FROM app_mgt_app_info amai
                    JOIN app_mgt_app_module amam ON amai.id = amam.app_id
                    JOIN team_mgt_app_bind tmab ON amai.id = tmab.app_id
                    JOIN team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
                    JOIN auth_user auser ON auser.username = tmub.user_name
                WHERE tmub.bind_is_active = 1
                    AND amam.need_online = 1
                GROUP BY amam.module_name
            ) AS producer_responsible ON producer.appName = producer_responsible.appName
            LEFT JOIN (
                SELECT amam.module_name AS appName,
                    GROUP_CONCAT(
                        CONCAT(auser.last_name, auser.first_name)
                        ORDER BY auser.last_name,
                            auser.first_name SEPARATOR ', '
                    ) AS userNames
                FROM app_mgt_app_info amai
                    JOIN app_mgt_app_module amam ON amai.id = amam.app_id
                    JOIN team_mgt_app_bind tmab ON amai.id = tmab.app_id
                    JOIN team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
                    JOIN auth_user auser ON auser.username = tmub.user_name
                WHERE tmub.bind_is_active = 1
                    AND amam.need_online = 1
                GROUP BY amam.module_name
            ) AS consumer_responsible ON consumer.appName = consumer_responsible.appName
        WHERE producer.relationShip = 'producer'
            AND producer.topicName IN (
                SELECT topicName
                FROM rmq_migrate_pqc_canal
                WHERE relationShip = 'producer'
                GROUP BY topicName
                HAVING COUNT(*) = 1
            )
              order by  consumer_responsible.userNames,producer.topicName;







-- 创建用户信息临时表，避免重复子查询
DROP  TABLE IF EXISTS temp_user_info;
CREATE  TABLE temp_user_info AS
SELECT
    amam.module_name AS appName,
    GROUP_CONCAT(
        CONCAT(auser.last_name, auser.first_name)
        ORDER BY auser.last_name, auser.first_name
        SEPARATOR ', '
    ) AS userNames
FROM app_mgt_app_info amai
    JOIN app_mgt_app_module amam ON amai.id = amam.app_id
    JOIN team_mgt_app_bind tmab ON amai.id = tmab.app_id
    JOIN team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
    JOIN auth_user auser ON auser.username = tmub.user_name
WHERE tmub.bind_is_active = 1
    AND amam.need_online = 1
GROUP BY amam.module_name;

-- 创建单一生产者主题临时表
DROP  TABLE IF EXISTS temp_single_producer_topics;
CREATE  TABLE temp_single_producer_topics AS
SELECT topicName
FROM rmq_migrate_pqc_canal
WHERE relationShip = 'producer'
GROUP BY topicName
HAVING COUNT(*) = 1;

-- 最终查询结果
SELECT DISTINCT
    producer.appName AS '生产者应用名称',
    p_user.userNames AS '生产者负责人',
    producer.topicName AS '主题名称',
    consumer.appName AS '消费者应用名称',
    c_user.userNames AS '消费者负责人',
    consumer.messageModel AS '消费模式',
    '未知' as '配置在哪'
FROM rmq_migrate_pqc_canal AS producer
    LEFT JOIN rmq_migrate_pqc_canal AS consumer
        ON producer.topicName = consumer.topicName
        AND consumer.relationShip = 'consumer'
    LEFT JOIN temp_user_info AS p_user
        ON producer.appName = p_user.appName
    LEFT JOIN temp_user_info AS c_user
        ON consumer.appName = c_user.appName
WHERE producer.relationShip = 'producer'
    AND producer.topicName IN (SELECT topicName FROM temp_single_producer_topics)
ORDER BY c_user.userNames, producer.topicName;