-- 创建用户信息临时表，避免重复子查询
DROP  TABLE IF EXISTS temp_user_info;
CREATE  TABLE temp_user_info AS
SELECT
    amam.module_name AS appName,
    GROUP_CONCAT(
        CONCAT(auser.last_name, auser.first_name)
        ORDER BY auser.last_name, auser.first_name
        SEPARATOR ', '
    ) AS userNames
FROM app_mgt_app_info amai
    JOIN app_mgt_app_module amam ON amai.id = amam.app_id
    JOIN team_mgt_app_bind tmab ON amai.id = tmab.app_id
    JOIN team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
    JOIN auth_user auser ON auser.username = tmub.user_name
WHERE tmub.bind_is_active = 1
    AND amam.need_online = 1
GROUP BY amam.module_name;

-- 创建单一生产者主题临时表
DROP  TABLE IF EXISTS temp_single_producer_topics;
CREATE  TABLE temp_single_producer_topics AS
SELECT topicName
FROM rmq_migrate_pqc_canal
WHERE relationShip = 'producer'
GROUP BY topicName
HAVING COUNT(*) = 1;

-- 最终查询结果
SELECT DISTINCT
    producer.appName AS '生产者应用名称',
    p_user.userNames AS '生产者负责人',
    producer.topicName AS '主题名称',
    consumer.appName AS '消费者应用名称',
    c_user.userNames AS '消费者负责人',
    consumer.messageModel AS '消费模式',
    '未知' as '配置在哪'
FROM rmq_migrate_pqc_canal AS producer
    LEFT JOIN rmq_migrate_pqc_canal AS consumer
        ON producer.topicName = consumer.topicName
        AND consumer.relationShip = 'consumer'
    LEFT JOIN temp_user_info AS p_user
        ON producer.appName = p_user.appName
    LEFT JOIN temp_user_info AS c_user
        ON consumer.appName = c_user.appName
WHERE producer.relationShip = 'producer'
    AND producer.topicName IN (SELECT topicName FROM temp_single_producer_topics)
ORDER BY c_user.userNames, producer.topicName;