# 查询业务集群主题关系 多生产者
-- 创建用户信息临时表，避免重复子查询
DROP TABLE IF EXISTS rmq_migrate_user_info_multi;
CREATE TABLE rmq_migrate_user_info_multi AS
SELECT
    amam.module_name AS appName,
    GROUP_CONCAT(CONCAT(auser.last_name, auser.first_name) 
                ORDER BY auser.last_name, auser.first_name 
                SEPARATOR ', ') AS userNames
FROM app_mgt_app_info amai
    JOIN app_mgt_app_module amam ON amai.id = amam.app_id
    JOIN team_mgt_app_bind tmab ON amai.id = tmab.app_id
    JOIN team_mgt_user_bind tmub ON tmab.team_id = tmub.team_id
    JOIN auth_user auser ON auser.username = tmub.user_name
WHERE tmub.bind_is_active = 1
    AND amam.need_online = 1
GROUP BY amam.module_name;

-- 创建多生产者主题临时表
DROP TABLE IF EXISTS rmq_migrate_multi_producer_topics;
CREATE TABLE rmq_migrate_multi_producer_topics AS
SELECT topicName
FROM rmq_migrate_pqc
WHERE relationShip = 'producer'
GROUP BY topicName
HAVING COUNT(*) > 1;

-- 创建去重的生产者-主题关系临时表
DROP TABLE IF EXISTS rmq_migrate_distinct_producers;
CREATE TABLE rmq_migrate_distinct_producers AS
SELECT DISTINCT
    producer.appName,
    producer.topicName
FROM rmq_migrate_pqc producer
WHERE producer.relationShip = 'producer'
    AND producer.topicName IN (SELECT topicName FROM rmq_migrate_multi_producer_topics);

-- 创建去重的消费者-主题关系临时表
DROP TABLE IF EXISTS rmq_migrate_distinct_consumers;
CREATE TABLE rmq_migrate_distinct_consumers AS
SELECT DISTINCT
    consumer.appName,
    consumer.topicName,
    consumer.messageModel
FROM rmq_migrate_pqc consumer
WHERE consumer.relationShip = 'consumer'
    AND consumer.topicName IN (SELECT topicName FROM rmq_migrate_multi_producer_topics);

-- 最终查询结果
SELECT
    p.appName AS '生产者应用名称',
    p_user.userNames AS '生产者负责人',
    p.topicName AS '主题名称',
    c.appName AS '消费者应用名称',
    c_user.userNames AS '消费者负责人',
    c.messageModel AS '消费模式'
FROM rmq_migrate_distinct_producers p
LEFT JOIN rmq_migrate_distinct_consumers c ON p.topicName = c.topicName
LEFT JOIN rmq_migrate_user_info_multi p_user ON p.appName = p_user.appName
LEFT JOIN rmq_migrate_user_info_multi c_user ON c.appName = c_user.appName
ORDER BY p.topicName, p_user.userNames, p.appName;