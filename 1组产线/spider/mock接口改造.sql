select distinct nb.module_name  ,i.br_name ,n.node_ip,s.suite_code,nb.node_lib_repo_update_time
from env_mgt_node_bind nb
         left join env_mgt_suite s on nb.suite_id = s.id
         left join env_mgt_region r on s.region_id = r.id
         left join env_mgt_deploy_group g on nb.deploy_group = g.id
         left join env_mgt_node n on n.id = nb.node_id
         left join product_mgt_product_info p ON nb.lib_repo_info_id=p.id
         left join iter_mgt_iter_info i ON i.pipeline_id = p.iteration_id
         left join app_mgt_app_module amam on nb.module_name = amam.module_name
where r.region_group = 'prod'
  and n.node_status = '0'
  and i.br_name is not null
  and nb.module_name in ('adviser-batch-center-remote',
'crm-asset',
'crm-asset-remote',
'crm-inst-data',
'crm-nt-server',
'crm-td-server',
'crm-wechat-remote',
'cs-task-server',
'schedule-ec-server',
'about',
'cgi-howbuy',
'cms-simu',
'cooperation',
'fund-boot',
'howbuy-ams-server',
'howbuy-cms-server',
'howbuy-content-server',
'howbuy-es-data',
'howbuy-fund-server',
'howbuy-grayscale-cms',
'howbuy-member-remote',
'howbuy-simu-server',
'howbuy-web-server',
'howbuy-wireless-cms',
'howbuycms-boot',
'member-center',
'simu-boot',
'wap-boot',
'website-boot',
'fisp-pre-remote',
'o32-pre-web',
'otc-batch-remote',
'otc-bop',
'otc-center-remote',
'otc-cms',
'otc-console',
'otc-counter',
'otc-dataservice-server',
'otc-direct-pre-server',
'otc-web',
'activity-center-remote',
'asset-batch-center-remote',
'asset-center-remote',
'batch-center-remote',
'center-member-service',
'cgi-ehowbuy-container',
'cgi-gateway',
'cgi-simu-container',
'coop-admin-remote',
'coop-cgi-container',
'coop-cgi-remote',
'coop-promotion-remote',
'coupon-center-remote',
'doctor',
'doctor-web',
'ehowbuy-web',
'elasticsearch-center-remote',
'ftx-batch-remote',
'ftx-order-remote',
'high-batch-center-remote',
'high-order-search-remote',
'high-order-trade-remote',
'howbuy-act-remote',
'howbuy-huodong-cms',
'howbuy-interlayer-console',
'howbuy-trade-weixin',
'huodongserver',
'howbuy-trade-activity',
'member-server-remote',
'message-center-remote',
'message-manage-server',
'order-center-remote',
'order-plan-center-remote',
'pension-batch-remote',
'pension-order-remote',
'product-center-remote',
'robot-order-center-remote',
'tms-counter-console',
'trade-cgi-gateway',
'crm-hb-webapp',
'export-proxy-gateway',
'otc-center-search-remote',
'xinfangcheng')
order by nb.module_name;
















select nb.module_name  ,i.br_name ,n.node_ip,s.suite_code,nb.node_lib_repo_update_time
from env_mgt_node_bind nb
         left join env_mgt_suite s on nb.suite_id = s.id
         left join env_mgt_region r on s.region_id = r.id
         left join env_mgt_deploy_group g on nb.deploy_group = g.id
         left join env_mgt_node n on n.id = nb.node_id
         left join product_mgt_product_info p ON nb.lib_repo_info_id=p.id
         left join iter_mgt_iter_info i ON i.pipeline_id = p.iteration_id
         left join app_mgt_app_module amam on nb.module_name = amam.module_name

where r.region_group = 'prod'
  and n.node_status = '0'
  and nb.module_name in ('cms-simu');

select nb.module_name  ,i.br_name ,n.node_ip,s.suite_code,nb.node_lib_repo_update_time
from env_mgt_node_bind nb
         left join env_mgt_suite s on nb.suite_id = s.id
         left join env_mgt_region r on s.region_id = r.id
         left join env_mgt_deploy_group g on nb.deploy_group = g.id
         left join env_mgt_node n on n.id = nb.node_id
         left join product_mgt_product_info p ON nb.lib_repo_info_id=p.id
         left join iter_mgt_iter_info i ON i.pipeline_id = p.iteration_id
         left join app_mgt_app_module amam on nb.module_name = amam.module_name

where r.region_group = 'prod'
  and n.node_status = '0'
  and nb.module_name in ('cgi-ehowbuy-container',
'cgi-gateway',
'cgi-simu-container',
'coop-cgi-container',
'howbuy-interlayer-console',
'howbuycms-boot',
'member-center',
'otc-bop',
'otc-cms',
'otc-console',
'otc-counter',
'otc-web',
'simu-boot',
'tms-counter-console',
'trade-cgi-gateway',
'about',
'activity-center-remote',
'adviser-batch-center-remote',
'asset-batch-center-remote',
'batch-center-remote',
'center-member-service',
'cgi-howbuy',
'cms-simu',
'coop-admin-remote',
'coop-promotion-remote',
'coupon-center-remote',
'crm-hb-webapp',
'crm-inst-data',
'crm-nt-server',
'crm-td-server',
'crm-wechat-remote',
'cs-task-server',
'doctor',
'elasticsearch-center-remote',
'fisp-pre-remote',
'ftx-batch-remote',
'ftx-order-remote',
'hb-wechat-server',
'high-batch-center-remote',
'howbuy-act-remote',
'howbuy-ams-server',
'howbuy-cms-server',
'howbuy-content-server',
'howbuy-es-data',
'howbuy-fund-server',
'howbuy-grayscale-cms',
'howbuy-middleware-remote',
'howbuy-simu-server',
'howbuy-web-server',
'howbuy-wireless-cms',
'member-server-remote',
'message-center-remote',
'message-manage-server',
'o32-pre-web',
'order-plan-center-remote',
'otc-batch-remote',
'otc-center-search-remote',
'pension-batch-remote',
'pension-order-remote',
'product-center-remote',
'robot-order-center-remote',
'schedule-ec-server',
'wap-boot',
'xinfangcheng',
'howbuy-member-remote',
'cooperation',
'fund-boot',
'website-boot',
'coop-cgi-remote',
'export-proxy-gateway',
'howbuy-trade-weixin',
'order-center-remote',
'asset-center-remote',
'high-order-search-remote',
'high-order-trade-remote',
'crm-asset',
'crm-asset-remote',
'otc-center-remote')
order by nb.module_name, nb.node_lib_repo_update_time,nb.module_name;


select module_name,br_name,group_concat(suite_code,node_ip),count(1) from (
    select distinct nb.module_name  ,i.br_name ,n.node_ip,s.suite_code,nb.node_lib_repo_update_time
from env_mgt_node_bind nb
         left join env_mgt_suite s on nb.suite_id = s.id
         left join env_mgt_region r on s.region_id = r.id
         left join env_mgt_deploy_group g on nb.deploy_group = g.id
         left join env_mgt_node n on n.id = nb.node_id
         left join product_mgt_product_info p ON nb.lib_repo_info_id=p.id
         left join iter_mgt_iter_info i ON i.pipeline_id = p.iteration_id
         left join app_mgt_app_module amam on nb.module_name = amam.module_name
where r.region_group = 'prod'
  and n.node_status = '0'
  and i.br_name is not null
order by nb.module_name
              ) temp
group by module_name,br_name
having count(1)>1