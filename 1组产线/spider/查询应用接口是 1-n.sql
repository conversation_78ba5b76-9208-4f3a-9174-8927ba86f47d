select interface_info.interface_path, interface_info.interface_method,interface_info.module_name
from (select distinct interface_path, interface_method,interface_info.module_name
      from app_mgt_interface_info interface_info
join
(select module_name  ,br_name,count(1) from (
select distinct nb.module_name  ,i.br_name
from env_mgt_node_bind nb
         left join env_mgt_suite s on nb.suite_id = s.id
         left join env_mgt_region r on s.region_id = r.id
         left join env_mgt_deploy_group g on nb.deploy_group = g.id
         left join env_mgt_node n on n.id = nb.node_id
         left join product_mgt_product_info p ON nb.lib_repo_info_id=p.id
         left join iter_mgt_iter_info i ON i.pipeline_id = p.iteration_id
         left join app_mgt_app_module amam on nb.module_name = amam.module_name
where r.region_group = 'prod'
  and n.node_status = '0'
)  t
group by t.module_name
having count(1) >= 1) prod_app_version on interface_info.module_name = prod_app_version.module_name and interface_info.branch_name = prod_app_version.br_name

      ) interface_info
group by interface_info.interface_path, interface_info.interface_method
having count(1) >=1;



select distinct  temp.interface_path, temp.module_names from (
select interface_info.interface_path, interface_info.interface_method,group_concat(interface_info.module_name) module_names
from (select distinct interface_path, interface_method,interface_info.module_name
      from app_mgt_interface_info interface_info
join
(select module_name  ,br_name,count(1) from (
select distinct nb.module_name  ,i.br_name
from env_mgt_node_bind nb
         left join env_mgt_suite s on nb.suite_id = s.id
         left join env_mgt_region r on s.region_id = r.id
         left join env_mgt_deploy_group g on nb.deploy_group = g.id
         left join env_mgt_node n on n.id = nb.node_id
         left join product_mgt_product_info p ON nb.lib_repo_info_id=p.id
         left join iter_mgt_iter_info i ON i.pipeline_id = p.iteration_id
         left join app_mgt_app_module amam on nb.module_name = amam.module_name
where r.region_group = 'prod'
  and n.node_status = '0'
)  t
group by t.module_name
having count(1) >= 1) prod_app_version on interface_info.module_name = prod_app_version.module_name and interface_info.branch_name = prod_app_version.br_name

      ) interface_info
group by interface_info.interface_path, interface_info.interface_method
having count(1) >=1) temp;



##查询每个应用的所有接口
select interface_info.interface_path,
       interface_info.interface_method,
       interface_info.interface_type,
       interface_info.module_name
from app_mgt_interface_info interface_info
group by interface_info.interface_path, interface_info.interface_method;



