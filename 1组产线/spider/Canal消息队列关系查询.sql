SELECT producer.appName AS '生产者应用名称',
    producer_responsible.userNames AS '生产者负责人',
    producer.topicName AS '主题名称',
    consumer.appName AS '消费者应用名称',
    consumer_responsible.userNames AS '消费者负责人'
FROM rmq_migrate_pqc_canal AS producer
    LEFT JOIN rmq_migrate_pqc_canal AS consumer ON producer.topicName = consumer.topicName
    AND consumer.relationShip = 'consumer'
    LEFT JOIN (...) AS producer_responsible ON producer.appName = producer_responsible.appName
    LEFT JOIN (...) AS consumer_responsible ON consumer.appName = consumer_responsible.appName
WHERE producer.relationShip = 'producer'
    AND producer.topicName IN (...)
ORDER BY producer.topicName;