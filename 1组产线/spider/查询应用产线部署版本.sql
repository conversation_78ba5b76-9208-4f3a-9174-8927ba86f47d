select module_name  ,br_name,count(1) from (
select distinct nb.module_name  ,i.br_name
from env_mgt_node_bind nb
         left join env_mgt_suite s on nb.suite_id = s.id
         left join env_mgt_region r on s.region_id = r.id
         left join env_mgt_deploy_group g on nb.deploy_group = g.id
         left join env_mgt_node n on n.id = nb.node_id
         left join product_mgt_product_info p ON nb.lib_repo_info_id=p.id
         left join iter_mgt_iter_info i ON i.pipeline_id = p.iteration_id
         left join app_mgt_app_module amam on nb.module_name = amam.module_name

where r.region_group = 'prod'
  and n.node_status = '0'
  and nb.module_name in ('cgi-ehowbuy-container',
'cgi-gateway',
'cgi-simu-container',
'coop-cgi-container',
'howbuy-interlayer-console',
'howbuycms-boot',
'member-center',
'otc-bop',
'otc-cms',
'otc-console',
'otc-counter',
'otc-web',
'simu-boot',
'tms-counter-console',
'trade-cgi-gateway',
'about',
'activity-center-remote',
'adviser-batch-center-remote',
'asset-batch-center-remote',
'batch-center-remote',
'center-member-service',
'cgi-howbuy',
'cms-simu',
'coop-admin-remote',
'coop-promotion-remote',
'coupon-center-remote',
'crm-hb-webapp',
'crm-inst-data',
'crm-nt-server',
'crm-td-server',
'crm-wechat-remote',
'cs-task-server',
'doctor',
'elasticsearch-center-remote',
'fisp-pre-remote',
'ftx-batch-remote',
'ftx-order-remote',
'hb-wechat-server',
'high-batch-center-remote',
'howbuy-act-remote',
'howbuy-ams-server',
'howbuy-cms-server',
'howbuy-content-server',
'howbuy-es-data',
'howbuy-fund-server',
'howbuy-grayscale-cms',
'howbuy-middleware-remote',
'howbuy-simu-server',
'howbuy-web-server',
'howbuy-wireless-cms',
'member-server-remote',
'message-center-remote',
'message-manage-server',
'o32-pre-web',
'order-plan-center-remote',
'otc-batch-remote',
'otc-center-search-remote',
'pension-batch-remote',
'pension-order-remote',
'product-center-remote',
'robot-order-center-remote',
'schedule-ec-server',
'wap-boot',
'xinfangcheng',
'howbuy-member-remote',
'cooperation',
'fund-boot',
'website-boot',
'coop-cgi-remote',
'export-proxy-gateway',
'howbuy-trade-weixin',
'order-center-remote',
'asset-center-remote',
'high-order-search-remote',
'high-order-trade-remote',
'crm-asset',
'crm-asset-remote',
'otc-center-remote')
)  t
group by t.module_name
having count(1) > 1;


select distinct s.suite_code as `环境`
from env_mgt_node_bind nb
         left join env_mgt_suite s on nb.suite_id = s.id
         left join env_mgt_region r on s.region_id = r.id
         left join env_mgt_deploy_group g on nb.deploy_group = g.id
         left join env_mgt_node n on n.id = nb.node_id
         left join product_mgt_product_info p ON nb.lib_repo_info_id=p.id
         left join iter_mgt_iter_info i ON i.pipeline_id = p.iteration_id
         left join app_mgt_app_module amam on nb.module_name = amam.module_name

where r.region_group = 'prod'
  and n.node_status = '0'
  and nb.module_name in ('asset-batch-center-remote',
                         'asset-center-remote',
                         'batch-center-remote',
                         'cgi-simu-container',
                         'cms-simu',
                         'doctor',
                         'high-batch-center-remote',
                         'high-order-search-remote',
                         'high-order-trade-remote',
                         'product-center-remote')
ORDER BY `应用名`,zeus_type,`环境` DESC;




select * from app_mgt_app_module;

SELECT i.br_name,b.node_lib_repo_update_time FROM env_mgt_node_bind b LEFT JOIN
env_mgt_suite s ON b.suite_id = s.id LEFT JOIN
product_mgt_product_info p ON b.lib_repo_info_id=p.id
LEFT JOIN iter_mgt_iter_info i ON i.pipeline_id = p.iteration_id
WHERE b.module_name = "{}" AND s.suite_code = "{}"  AND i.br_name IS NOT NULL
ORDER BY b.node_lib_repo_update_time DESC;


create table zeus_app_module(
    module_name varchar(255) not null,
    br_name varchar(255) not null,
    zeus_type varchar(255) not null,
    primary key (module_name,zeus_type)
);

delete from zeus_app_module;
 insert into zeus_app_module(module_name,br_name,zeus_type)

select distinct nb.module_name  as app_name,
                s.suite_code    as `tenant_id`,
                i.br_name       as `version`,
                '2024091501'    as snapshot_num,
                'replaceConfig' as `type`
from env_mgt_node_bind nb
         left join env_mgt_suite s on nb.suite_id = s.id
         left join env_mgt_region r on s.region_id = r.id
         left join env_mgt_deploy_group g on nb.deploy_group = g.id
         left join env_mgt_node n on n.id = nb.node_id
         left join product_mgt_product_info p ON nb.lib_repo_info_id = p.id
         left join iter_mgt_iter_info i ON i.pipeline_id = p.iteration_id
         left join app_mgt_app_module amam on nb.module_name = amam.module_name
where r.region_group = 'prod'
  and n.node_status = '0'
 and nb.module_name in (
'amsreport',
'asset-batch-center-remote',
'calc-offline-fund',
'calc-offline-high',
'calc-offline-portfolio',
'cgi-howbuy',
'cms-simu',
'fpc-manage-admin',
'fpc-manage-console',
'fpc-manage-data',
'fpc-manage-report',
'fund-boot',
'howbuy-ams-server',
'howbuycms-boot',
'howbuy-cms-server',
'howbuy-content-server',
'howbuy-crm-sync',
'howbuy-data-ants',
'howbuy-es-data',
'howbuy-fpc-das',
'howbuy-fpc-data',
'howbuy-fpc-monitor',
'howbuy-fpc-notice',
'howbuy-fpc-quotation',
'howbuy-fpc-reptile',
'howbuy-fpc-scheduler',
'howbuy-fpc-submit',
'howbuy-fund-server',
'howbuy-grayscale-cms',
'howbuy-member-remote',
'howbuy-simu-server',
'howbuy-web-server',
'otc-dataservice-server',
'param-console'
    );


select distinct nb.module_name  as app_name,
                s.suite_code    as `tenant_id`,
                i.br_name       as `version`,
                '2024051801'    as snapshot_num,
                'replaceConfig' as `type`
from env_mgt_node_bind nb
         left join env_mgt_suite s on nb.suite_id = s.id
         left join env_mgt_region r on s.region_id = r.id
         left join env_mgt_deploy_group g on nb.deploy_group = g.id
         left join env_mgt_node n on n.id = nb.node_id
         left join product_mgt_product_info p ON nb.lib_repo_info_id = p.id
         left join iter_mgt_iter_info i ON i.pipeline_id = p.iteration_id
         left join app_mgt_app_module amam on nb.module_name = amam.module_name

where r.region_group = 'prod'
  and n.node_status = '0'
  and nb.module_name in ('howbuy-interlayer-console',
                         'cgi-ehowbuy-container',
                         'cgi-gateway',
                         'cgi-simu-container',
                         'coop-cgi-container',
                         'howbuycms-boot',
                         'member-center',
                         'otc-bop',
                         'otc-cms',
                         'otc-console',
                         'otc-counter',
                         'otc-web',
                         'simu-boot',
                         'tms-counter-console',
                         'trade-cgi-gateway')
ORDER BY `app_name`;

select count(*) from sdk_config_replace_app_deploy_info;
delete from sdk_config_replace_app_deploy_info;
delete from sdk_config_replace_app_deploy_info where app_name in ('huodongserver','howbuy-trade-activity','howbuy-huodong-cms','ehowbuy-web');
delete from sdk_config_replace_app_deploy_info where app_name ='cgi-simu-container' and version = '4.4.29';
delete from sdk_config_replace_app_deploy_info where app_name ='message-center-remote' and version = '4.2.5';
delete from sdk_config_replace_app_deploy_info where app_name ='howbuy-middleware-remote' and version = '4.2.5';

select count(*) from sdk_config_replace_app_deploy_info;#707
update sdk_config_replace_app_deploy_info set snapshot_num = '20240518T3' where snapshot_num ='20240518T3-1';
update sdk_config_replace_app_deploy_info set snapshot_num = '20240518T3-rollback' where snapshot_num ='20240518T3-1-rollback';

select distinct  snapshot_num,type  from sdk_config_replace_app_deploy_info;
select * from  sdk_config_replace_app_deploy_info  where snapshot_num ='20240518T3-rollback';







