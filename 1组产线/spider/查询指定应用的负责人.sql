
select amam.module_name as appName,group_concat(concat(auser.last_name,auser.first_name) ORDER BY auser.last_name, auser.first_name) as userNames from app_mgt_app_info  amai
         join app_mgt_app_module amam on amai.id = amam.app_id
         join team_mgt_app_bind tmab on amai.id = tmab.app_id
         join team_mgt_user_bind tmub on tmab.team_id = tmub.team_id
         join auth_user auser on auser.username = tmub.user_name
where tmub.bind_is_active=1
and amam.need_online=1
and amam.module_name="order-center-new-remote"
group by amam.module_name;