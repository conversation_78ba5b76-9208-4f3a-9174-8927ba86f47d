INSERT INTO zeus_tag_val (env_id,
                                tag_id,
                                et_val,
                                et_desc,
                                create_user,
                                create_time,
                                update_user,
                                update_time,
                                stamp)
SELECT
        distinct 1001 AS env_id,
                bs.tag_id,
                bs.et_val,
                bs.et_desc,
                bs.create_user,
                bs.create_time,
                bs.update_user,
                bs.update_time,
                bs.stamp
FROM zeus_tag_val bs
INNER JOIN zeus_env be
    ON bs.env_id = be.id
    AND be.tenant_id = 'tcloud-prod'
INNER JOIN zeus_tag zt
    ON bs.tag_id = zt.id
LEFT JOIN zeus_tag_val tcloud
    ON bs.tag_id = tcloud.tag_id
    AND tcloud.env_id IN (
        SELECT id
        FROM zeus_env
        WHERE tenant_id = 'bs-prod'
    )
LEFT JOIN zeus_tag_segment_dict s1
    ON zt.first_code = s1.id
LEFT JOIN zeus_tag_segment_dict s3
    ON zt.third_code = s3.id
WHERE tcloud.et_val IS NULL and bs.et_val IS NOT NULL;
