SELECT
    CONCAT(s1.code, '_', zt.second_code, '_', s3.code) AS '描述符',
    bs.et_val AS 'bs-prod',
    tcloud.et_val AS 'tcloud-prod'
FROM zeus_tag_val bs
INNER JOIN zeus_env be ON bs.env_id = be.id AND be.tenant_id = 'bs-prod'
INNER JOIN zeus_tag_val tcloud
    ON bs.tag_id = tcloud.tag_id
    AND tcloud.env_id = (SELECT id FROM zeus_env WHERE tenant_id = 'tcloud-prod' LIMIT 1)
INNER JOIN zeus_tag zt ON bs.tag_id = zt.id
LEFT JOIN zeus_tag_segment_dict s1 ON zt.first_code = s1.id
LEFT JOIN zeus_tag_segment_dict s3 ON zt.third_code = s3.id
WHERE bs.et_val != tcloud.et_val;

# 查询两个产线环境的描述符值不一致的描述符
SELECT bs.tag_id,
       CONCAT(s1.code, '_', zt.second_code, '_', s3.code) AS '描述符',
       bs.et_val                                          AS 'bs-prod',
       tcloud.et_val                                      AS 'tcloud-prod'
FROM zeus_tag_val bs
         INNER JOIN zeus_env be ON bs.env_id = be.id AND be.tenant_id = 'bs-prod'
         INNER JOIN zeus_tag_val tcloud
                    ON bs.tag_id = tcloud.tag_id
                        AND tcloud.env_id = (SELECT id FROM zeus_env WHERE tenant_id = 'tcloud-prod' LIMIT 1)
         INNER JOIN zeus_tag zt ON bs.tag_id = zt.id
         LEFT JOIN zeus_tag_segment_dict s1 ON zt.first_code = s1.id
         LEFT JOIN zeus_tag_segment_dict s3 ON zt.third_code = s3.id
WHERE bs.et_val != tcloud.et_val
  and bs.tag_id in (select zci.tag_id
                    from zeus_config_instance zci
                             join zeus_app zpp on zci.app_id = zpp.id
                             join app_info_devops on zpp.name = app_info_devops.app_name
                    where zci.version = app_info_devops.last_archived
                      and zci.is_env_tag = 1);
order by `描述符`;


INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('939004671619', '1001', 'zookeeper://qqzk-1.inner.howbuy.com:2181?backup=qqzk-2.inner.howbuy.com:2181,qqzk-3.inner.howbuy.com:2181,qqzk-4.inner.howbuy.com:2181,qqzk-5.inner.howbuy.com:2181', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('950282431619', '1001', '{"needLocalCache": false, "remoteCacheType": "redis", "addrList": "simu-redis.inner.howbuy.com:6379", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('952091731614', '1001', '********************************************', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('953149951585', '1001', '**********************************************************************************************************************************', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('958190931584', '1001', '*************************************************************************************************************************************', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('958529831616', '1001', '*********************************************************************************************************************************', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1038178001585', '1001', '***************************************************************************************************************************************', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1501030251587', '1001', 'bs-prod', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1523030251581', '1001', '{"hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps", "addrList": "ams-redis6.inner.howbuy.com:6379", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "needLocalCache": false, "remoteCacheType": "redis"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1533429801582', '1001', '*******************************************************************************************************************************************************', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1636282961612', '1001', '{"needLocalCache": true, "remoteCacheType": "redis", "addrList": "cms-redis6.inner.howbuy.com:6379", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1643485921582', '1001', '{"needLocalCache": true, "remoteCacheType": "redis", "addrList": "simu-redis6.inner.howbuy.com:6379", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1644521081588', '1001', '{"needLocalCache": false, "remoteCacheType": "redis", "addrList": "simu-redis6.inner.howbuy.com:6379", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1707071261618', '1001', '{"hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps", "addrList": "cms-redis6.inner.howbuy.com:6379", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "needLocalCache": false, "remoteCacheType": "redis"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1709073561614', '1001', '{"remoteCacheType": "redis", "addrList": "web2-redis6.inner.howbuy.com:6379", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1709306551586', '1001', '{"remoteCacheType": "redis", "addrList": "shard.001-1.redis6.inner.howbuy.com:6379,shard.001-2.redis6.inner.howbuy.com:6379,shard.001-3.redis6.inner.howbuy.com:6379,shard.001-4.redis6.inner.howbuy.com:6379", "clusterType": "shards", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1710529381585', '1001', '{"hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps", "addrList": "ams-redis6.inner.howbuy.com:6379", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "needLocalCache": false, "remoteCacheType": "redis"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1921073891619', '1001', 'elastic-data2.inner.howbuy.com:80', 'synchronous');




