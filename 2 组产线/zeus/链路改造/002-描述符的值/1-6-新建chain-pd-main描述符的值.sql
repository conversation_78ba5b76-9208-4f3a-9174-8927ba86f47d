INSERT INTO zeus_tag_val (env_id,
                                tag_id,
                                et_val,
                                et_desc,
                                create_user,
                                create_time,
                                update_user,
                                update_time,
                                stamp)
select distinct 1003 AS env_id,
       ztv.tag_id,
       ztv.et_val,
       ztv.et_desc,
       ztv.create_user,
       ztv.create_time,
       ztv.update_user,
       ztv.update_time,
       ztv.stamp
from zeus_tag_val ztv
         INNER JOIN zeus_env zenv ON ztv.env_id = zenv.id
WHERE zenv.tenant_id = 'pd-prod';
