INSERT INTO zeus_tag_val (env_id,
                                tag_id,
                                et_val,
                                et_desc,
                                create_user,
                                create_time,
                                update_user,
                                update_time,
                                stamp)
select distinct 1002 AS env_id,
                ztv.tag_id,
                ztv.et_val,
                ztv.et_desc,
                ztv.create_user,
                ztv.create_time,
                ztv.update_user,
                ztv.update_time,
                ztv.stamp
from zeus_tag_val ztv
         INNER JOIN zeus_env zenv ON ztv.env_id = zenv.id
WHERE zenv.tenant_id = 'tcloud-prod'
  and tag_id in (select distinct zci.tag_id
                 from zeus_config_instance zci
                          INNER JOIN zeus_env zenv ON zci.env_id = zenv.id
                          INNER JOIN zeus_app zapp on zapp.id = zci.app_id
                          INNER JOIN app_info_devops aid on aid.app_name = zapp.name
                 where zenv.tenant_id = 'tcloud-prod'
                   and zci.tag_id is not null
                   and zci.is_env_tag = 1
                   and zapp.name = 'howbuy-fund-server'
                   and zci.version = aid.last_archived);
