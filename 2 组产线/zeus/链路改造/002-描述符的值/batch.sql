INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('939004671619', '1001', 'zookeeper://qqzk-1.inner.howbuy.com:2181?backup=qqzk-2.inner.howbuy.com:2181,qqzk-3.inner.howbuy.com:2181,qqzk-4.inner.howbuy.com:2181,qqzk-5.inner.howbuy.com:2181', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('950282431619', '1001', '{"needLocalCache": false, "remoteCacheType": "redis", "addrList": "simu-redis.inner.howbuy.com:6379", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('952091731614', '1001', '********************************************', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('953149951585', '1001', '**********************************************************************************************************************************', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('958190931584', '1001', '*************************************************************************************************************************************', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('958529831616', '1001', '*********************************************************************************************************************************', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1038178001585', '1001', '***************************************************************************************************************************************', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1501030251587', '1001', 'bs-prod', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1523030251581', '1001', '{"hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps", "addrList": "ams-redis6.inner.howbuy.com:6379", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "needLocalCache": false, "remoteCacheType": "redis"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1533429801582', '1001', '*******************************************************************************************************************************************************', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1636282961612', '1001', '{"needLocalCache": true, "remoteCacheType": "redis", "addrList": "cms-redis6.inner.howbuy.com:6379", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1643485921582', '1001', '{"needLocalCache": true, "remoteCacheType": "redis", "addrList": "simu-redis6.inner.howbuy.com:6379", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1644521081588', '1001', '{"needLocalCache": false, "remoteCacheType": "redis", "addrList": "simu-redis6.inner.howbuy.com:6379", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1707071261618', '1001', '{"hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps", "addrList": "cms-redis6.inner.howbuy.com:6379", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "needLocalCache": false, "remoteCacheType": "redis"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1709073561614', '1001', '{"remoteCacheType": "redis", "addrList": "web2-redis6.inner.howbuy.com:6379", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1709306551586', '1001', '{"remoteCacheType": "redis", "addrList": "shard.001-1.redis6.inner.howbuy.com:6379,shard.001-2.redis6.inner.howbuy.com:6379,shard.001-3.redis6.inner.howbuy.com:6379,shard.001-4.redis6.inner.howbuy.com:6379", "clusterType": "shards", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1710529381585', '1001', '{"hpsUrl": "http://hps.intelnal.howbuy.com/hps/ps", "addrList": "ams-redis6.inner.howbuy.com:6379", "password": "Howbuy@2021!", "passwordKey": "REDIS.ec.cache.common.PassWord", "needLocalCache": false, "remoteCacheType": "redis"}', 'synchronous');
INSERT INTO zeus_tag_val (tag_id, env_id, et_val, create_user) VALUES ('1921073891619', '1001', 'elastic-data2.inner.howbuy.com:80', 'synchronous');

INSERT INTO zeus_tag_val (env_id,
                          tag_id,
                          et_val,
                          et_desc,
                          create_user,
                          create_time,
                          update_user,
                          update_time,
                          stamp)
SELECT distinct 1001 AS env_id,
                bs.tag_id,
                bs.et_val,
                bs.et_desc,
                bs.create_user,
                bs.create_time,
                bs.update_user,
                bs.update_time,
                bs.stamp
FROM zeus_tag_val bs
         INNER JOIN zeus_env be ON bs.env_id = be.id AND be.tenant_id = 'bs-prod'
         INNER JOIN zeus_tag_val tcloud
                    ON bs.tag_id = tcloud.tag_id
                        AND tcloud.env_id = (SELECT id FROM zeus_env WHERE tenant_id = 'tcloud-prod' LIMIT 1)
         INNER JOIN zeus_tag zt ON bs.tag_id = zt.id
         LEFT JOIN zeus_tag_segment_dict s1 ON zt.first_code = s1.id
         LEFT JOIN zeus_tag_segment_dict s3 ON zt.third_code = s3.id
WHERE bs.et_val = tcloud.et_val;


INSERT INTO zeus_tag_val (env_id,
                          tag_id,
                          et_val,
                          et_desc,
                          create_user,
                          create_time,
                          update_user,
                          update_time,
                          stamp)
SELECT
    distinct 1001 AS env_id,
             bs.tag_id,
             bs.et_val,
             bs.et_desc,
             bs.create_user,
             bs.create_time,
             bs.update_user,
             bs.update_time,
             bs.stamp
FROM zeus_tag_val bs
         INNER JOIN zeus_env be
                    ON bs.env_id = be.id
                        AND be.tenant_id = 'tcloud-prod'
         INNER JOIN zeus_tag zt
                    ON bs.tag_id = zt.id
         LEFT JOIN zeus_tag_val tcloud
                   ON bs.tag_id = tcloud.tag_id
                       AND tcloud.env_id IN (
                           SELECT id
                           FROM zeus_env
                           WHERE tenant_id = 'bs-prod'
                       )
         LEFT JOIN zeus_tag_segment_dict s1
                   ON zt.first_code = s1.id
         LEFT JOIN zeus_tag_segment_dict s3
                   ON zt.third_code = s3.id
WHERE tcloud.et_val IS NULL and bs.et_val IS NOT NULL;

INSERT INTO zeus_tag_val (env_id,
                          tag_id,
                          et_val,
                          et_desc,
                          create_user,
                          create_time,
                          update_user,
                          update_time,
                          stamp)
SELECT
    distinct 1001 AS env_id,
             bs.tag_id,
             bs.et_val,
             bs.et_desc,
             bs.create_user,
             bs.create_time,
             bs.update_user,
             bs.update_time,
             bs.stamp
FROM zeus_tag_val bs
         INNER JOIN zeus_env be
                    ON bs.env_id = be.id
                        AND be.tenant_id = 'bs-prod'
         INNER JOIN zeus_tag zt
                    ON bs.tag_id = zt.id
         LEFT JOIN zeus_tag_val tcloud
                   ON bs.tag_id = tcloud.tag_id
                       AND tcloud.env_id IN (
                           SELECT id
                           FROM zeus_env
                           WHERE tenant_id = 'tcloud-prod'
                       )
         LEFT JOIN zeus_tag_segment_dict s1
                   ON zt.first_code = s1.id
         LEFT JOIN zeus_tag_segment_dict s3
                   ON zt.third_code = s3.id
WHERE tcloud.et_val IS NULL and bs.et_val IS NOT NULL;


INSERT INTO zeus_tag_val (env_id,
                          tag_id,
                          et_val,
                          et_desc,
                          create_user,
                          create_time,
                          update_user,
                          update_time,
                          stamp)
select distinct 1002 AS env_id,
                ztv.tag_id,
                ztv.et_val,
                ztv.et_desc,
                ztv.create_user,
                ztv.create_time,
                ztv.update_user,
                ztv.update_time,
                ztv.stamp
from zeus_tag_val ztv
         INNER JOIN zeus_env zenv ON ztv.env_id = zenv.id
WHERE zenv.tenant_id = 'tcloud-prod'
  and tag_id in (select distinct zci.tag_id
                 from zeus_config_instance zci
                          INNER JOIN zeus_env zenv ON zci.env_id = zenv.id
                          INNER JOIN zeus_app zapp on zapp.id = zci.app_id
                          INNER JOIN app_info_devops aid on aid.app_name = zapp.name
                 where zenv.tenant_id = 'tcloud-prod'
                   and zci.tag_id is not null
                   and zci.is_env_tag = 1
                   and zapp.name = 'howbuy-fund-server'
                   and zci.version = aid.last_archived);



INSERT INTO zeus_tag_val (env_id,
                          tag_id,
                          et_val,
                          et_desc,
                          create_user,
                          create_time,
                          update_user,
                          update_time,
                          stamp)
select distinct 1003 AS env_id,
                ztv.tag_id,
                ztv.et_val,
                ztv.et_desc,
                ztv.create_user,
                ztv.create_time,
                ztv.update_user,
                ztv.update_time,
                ztv.stamp
from zeus_tag_val ztv
         INNER JOIN zeus_env zenv ON ztv.env_id = zenv.id
WHERE zenv.tenant_id = 'pd-prod';



INSERT INTO zeus_tag_val (env_id,
                          tag_id,
                          et_val,
                          et_desc,
                          create_user,
                          create_time,
                          update_user,
                          update_time,
                          stamp)
select distinct 1004 AS env_id,
                ztv.tag_id,
                ztv.et_val,
                ztv.et_desc,
                ztv.create_user,
                ztv.create_time,
                ztv.update_user,
                ztv.update_time,
                ztv.stamp
from zeus_tag_val ztv
         INNER JOIN zeus_env zenv ON ztv.env_id = zenv.id
WHERE zenv.tenant_id = 'wgq-zb' and ztv.et_val is  not null ;

# beta 独有的
INSERT INTO zeus_tag_val (env_id,
                          tag_id,
                          et_val,
                          et_desc,
                          create_user,
                          create_time,
                          update_user,
                          update_time,
                          stamp)
SELECT
    distinct 1004 AS env_id,
             bs.tag_id,
             bs.et_val,
             bs.et_desc,
             bs.create_user,
             bs.create_time,
             bs.update_user,
             bs.update_time,
             bs.stamp
FROM zeus_tag_val bs
         INNER JOIN zeus_env be
                    ON bs.env_id = be.id
                        AND be.tenant_id = 'beta'
         INNER JOIN zeus_tag zt
                    ON bs.tag_id = zt.id
         LEFT JOIN zeus_tag_val tcloud
                   ON bs.tag_id = tcloud.tag_id
                       AND tcloud.env_id IN (
                           SELECT id
                           FROM zeus_env
                           WHERE tenant_id = 'wgq-zb'
                       )
         LEFT JOIN zeus_tag_segment_dict s1
                   ON zt.first_code = s1.id
         LEFT JOIN zeus_tag_segment_dict s3
                   ON zt.third_code = s3.id
WHERE tcloud.et_val IS NULL and bs.et_val IS NOT NULL;






