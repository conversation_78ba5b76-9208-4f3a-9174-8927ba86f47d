INSERT INTO zeus_tag_val (env_id,
                                tag_id,
                                et_val,
                                et_desc,
                                create_user,
                                create_time,
                                update_user,
                                update_time,
                                stamp)
select distinct 1004 AS env_id,
       ztv.tag_id,
       ztv.et_val,
       ztv.et_desc,
       ztv.create_user,
       ztv.create_time,
       ztv.update_user,
       ztv.update_time,
       ztv.stamp
from zeus_tag_val ztv
         INNER JOIN zeus_env zenv ON ztv.env_id = zenv.id
WHERE zenv.tenant_id = 'wgq-zb' and ztv.et_val is  not null ;

# beta 独有的
INSERT INTO zeus_tag_val (env_id,
                                tag_id,
                                et_val,
                                et_desc,
                                create_user,
                                create_time,
                                update_user,
                                update_time,
                                stamp)
SELECT
        distinct 1004 AS env_id,
                bs.tag_id,
                bs.et_val,
                bs.et_desc,
                bs.create_user,
                bs.create_time,
                bs.update_user,
                bs.update_time,
                bs.stamp
FROM zeus_tag_val bs
INNER JOIN zeus_env be
    ON bs.env_id = be.id
    AND be.tenant_id = 'beta'
INNER JOIN zeus_tag zt
    ON bs.tag_id = zt.id
LEFT JOIN zeus_tag_val tcloud
    ON bs.tag_id = tcloud.tag_id
    AND tcloud.env_id IN (
        SELECT id
        FROM zeus_env
        WHERE tenant_id = 'wgq-zb'
    )
LEFT JOIN zeus_tag_segment_dict s1
    ON zt.first_code = s1.id
LEFT JOIN zeus_tag_segment_dict s3
    ON zt.third_code = s3.id
WHERE tcloud.et_val IS NULL and bs.et_val IS NOT NULL;

# 查询两个产线环境的描述符值不一致的描述符
SELECT bs.tag_id,
       CONCAT(s1.code, '_', zt.second_code, '_', s3.code) AS '描述符',
       bs.et_val                                          AS 'bs-prod',
       tcloud.et_val                                      AS 'tcloud-prod'
FROM zeus_tag_val bs
         INNER JOIN zeus_env be ON bs.env_id = be.id AND be.tenant_id = 'wgq-zb'
         INNER JOIN zeus_tag_val tcloud
                    ON bs.tag_id = tcloud.tag_id
                        AND tcloud.env_id = (SELECT id FROM zeus_env WHERE tenant_id = 'beta' LIMIT 1)
         INNER JOIN zeus_tag zt ON bs.tag_id = zt.id
         LEFT JOIN zeus_tag_segment_dict s1 ON zt.first_code = s1.id
         LEFT JOIN zeus_tag_segment_dict s3 ON zt.third_code = s3.id
WHERE bs.et_val != tcloud.et_val
  and bs.tag_id in (select zci.tag_id
                    from zeus_config_instance zci
                             join zeus_app zpp on zci.app_id = zpp.id
                             join app_info_devops on zpp.name = app_info_devops.app_name
                    where zci.version = app_info_devops.last_archived
                      and zci.is_env_tag = 1);
order by `描述符`;
