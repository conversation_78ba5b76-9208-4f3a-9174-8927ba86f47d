select distinct app_name as '应用名', version as '版本', suite_code as '环境'
from (select distinct aid.app_name, aid.last_archived as version, adi.suite_code
      from app_info_devops aid
               left join zeus_app azpp on aid.app_name = azpp.name
               left join app_deploy_info adi on aid.app_name = adi.module_name
      where adi.suite_code is not null
        and aid.last_archived is not null
      union all
      select distinct aid.app_name, aid.last_deployed as version, adi.suite_code
      from app_info_devops aid
               left join zeus_app azpp on aid.app_name = azpp.name
               left join app_deploy_info adi on aid.app_name = adi.module_name
      where adi.suite_code is not null
        and aid.last_deployed is not null) all_data
order by all_data.app_name, all_data.suite_code;



select distinct app_name as name, version, suite_code as env_name, ze.id as env_id, azpp.id as app_id
from (select distinct aid.app_name, aid.last_archived as version, adi.suite_code
      from app_info_devops aid
               left join zeus_app azpp on aid.app_name = azpp.name
               left join app_deploy_info adi on aid.app_name = adi.module_name
      where adi.suite_code is not null
        and aid.last_archived is not null
      union all
      select distinct aid.app_name, aid.last_deployed as version, adi.suite_code
      from app_info_devops aid
               left join zeus_app azpp on aid.app_name = azpp.name
               left join app_deploy_info adi on aid.app_name = adi.module_name
      where adi.suite_code is not null
        and aid.last_deployed is not null) all_data

         join zeus_env ze on all_data.suite_code = ze.tenant_id
         join zeus_app azpp on all_data.app_name = azpp.name
order by all_data.app_name, all_data.suite_code;









