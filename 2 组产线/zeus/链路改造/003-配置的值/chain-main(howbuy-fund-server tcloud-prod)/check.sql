# 创建临时表 -- 对比
DROP TABLE IF EXISTS compare_instance;
create table if not exists `compare_instance` like `zeus_config_instance`;
DROP TABLE IF EXISTS compare_instance_assemble;
create table if not exists `compare_instance_assemble` like `zeus_config_instance_assemble`;

# 查询不包括 cgi-gateway,howbuy-fund-server的所有配置
insert into `compare_instance`
(app_id,
 version,
 env_id,
 effect_env_id,
 `key`,
 value,
 `desc`,
 is_env_tag,
 tag_id,
 old_tag_id,
 prefix,
 old_prefix,
 group_name,
 status,
 assemble_val,
 create_user,
 create_time,
 update_user,
 update_time)
select instance.app_id,
       instance.version,
       1001 AS env_id,
       instance.effect_env_id,
       instance.`key`,
       instance.value,
       instance.`desc`,
       instance.is_env_tag,
       instance.tag_id,
       instance.old_tag_id,
       instance.prefix,
       instance.old_prefix,
       instance.group_name,
       instance.status,
       instance.assemble_val,
       instance.create_user,
       instance.create_time,
       instance.update_user,
       instance.update_time
from zeus_config_instance instance
         INNER JOIN zeus_app zapp ON instance.app_id = zapp.id
         INNER JOIN zeus_env zenv ON instance.env_id = zenv.id
WHERE zenv.tenant_id = 'tcloud-prod'
  AND zapp.name IN ('howbuy-fund-server');


# 查出compare_instance与zeus_config_instance_chain中 env_id=1001的记录不一样的数据，以 compare_instance 为准，两表的字段一模一样


-- 定义目标环境变量
SET @target_env = 1001;

-- 核心差异检测SQL
SELECT
    c.env_id,
    c.app_id,
    c.version,
    c.key,
    -- 字段差异标识 --
    IF(c.value <=> z.value, 0, 1) AS value_diff,
    IF(c.desc <=> z.desc, 0, 1) AS desc_diff,
    IF(c.is_env_tag <=> z.is_env_tag, 0, 1) AS is_env_tag_diff,
    IF(c.tag_id <=> z.tag_id, 0, 1) AS tag_id_diff,
    IF(c.old_tag_id <=> z.old_tag_id, 0, 1) AS old_tag_id_diff,
    IF(c.prefix <=> z.prefix, 0, 1) AS prefix_diff,
    IF(c.old_prefix <=> z.old_prefix, 0, 1) AS old_prefix_diff,
    IF(c.group_name <=> z.group_name, 0, 1) AS group_name_diff,
    IF(c.status <=> z.status, 0, 1) AS status_diff,
    IF(c.assemble_val <=> z.assemble_val, 0, 1) AS assemble_val_diff
FROM compare_instance c
LEFT JOIN zeus_config_instance z
    ON c.env_id = z.env_id
    AND c.app_id = z.app_id
    AND c.version = z.version
    AND c.key = z.key
    AND z.env_id = @target_env
WHERE c.env_id = @target_env
  AND (
      z.env_id IS NULL  -- 目标表不存在该记录
      OR NOT (
          COALESCE(c.value, '') <=> COALESCE(z.value, '')
          AND COALESCE(c.desc, '') <=> COALESCE(z.desc, '')
          AND COALESCE(c.is_env_tag, 0) <=> COALESCE(z.is_env_tag, 0)
          AND COALESCE(c.tag_id, 0) <=> COALESCE(z.tag_id, 0)
          AND COALESCE(c.old_tag_id, 0) <=> COALESCE(z.old_tag_id, 0)
          AND COALESCE(c.prefix, '') <=> COALESCE(z.prefix, '')
          AND COALESCE(c.old_prefix, '') <=> COALESCE(z.old_prefix, '')
          AND COALESCE(c.group_name, '') <=> COALESCE(z.group_name, '')
          AND COALESCE(c.status, 0) <=> COALESCE(z.status, 0)
          AND COALESCE(c.assemble_val, '') <=> COALESCE(z.assemble_val, '')
      )
  );

# 对比assemble表
insert into `compare_instance_assemble`
(app_id,
 env_id,
 `key`,
 value,
 is_env_tag,
 tag_id,
 prefix,
 group_name,
 create_user,
 create_time,
 update_user,
 update_time)
select assemble_instance.app_id,
       1001 AS env_id,
       assemble_instance.`key`,
       assemble_instance.value,
       assemble_instance.is_env_tag,
       assemble_instance.tag_id,
       assemble_instance.prefix,
       assemble_instance.group_name,
       assemble_instance.create_user,
       assemble_instance.create_time,
       assemble_instance.update_user,
       assemble_instance.update_time
from zeus_config_instance_assemble assemble_instance
         JOIN
     zeus_app zapp ON assemble_instance.app_id = zapp.id
         JOIN
     zeus_env zenv ON assemble_instance.env_id = zenv.id
WHERE zenv.tenant_id = 'tcloud-prod'
  AND zapp.name IN ('howbuy-fund-server');


-- 定义目标环境变量
SET @target_env = 1001;

-- 核心差异检测SQL
SELECT
    c.env_id,
    c.app_id,
    c.key,
    -- 字段差异标识 --
    IF(c.value <=> z.value, 0, 1) AS value_diff,
    IF(c.is_env_tag <=> z.is_env_tag, 0, 1) AS is_env_tag_diff,
    IF(c.tag_id <=> z.tag_id, 0, 1) AS tag_id_diff,
    IF(c.prefix <=> z.prefix, 0, 1) AS prefix_diff,
    IF(c.group_name <=> z.group_name, 0, 1) AS group_name_diff
FROM compare_instance_assemble c
LEFT JOIN zeus_config_instance_assemble z
    ON c.env_id = z.env_id
    AND c.app_id = z.app_id
    AND c.key = z.key
    AND z.env_id = @target_env
WHERE c.env_id = @target_env
  AND (
      z.env_id IS NULL  -- 目标表不存在该记录
      OR NOT (
          COALESCE(c.value, '') <=> COALESCE(z.value, '')
          AND COALESCE(c.is_env_tag, 0) <=> COALESCE(z.is_env_tag, 0)
          AND COALESCE(c.tag_id, 0) <=> COALESCE(z.tag_id, 0)
          AND COALESCE(c.prefix, '') <=> COALESCE(z.prefix, '')
          AND COALESCE(c.group_name, '') <=> COALESCE(z.group_name, '')
      )
  );
