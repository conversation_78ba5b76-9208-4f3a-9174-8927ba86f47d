# 查询不包括 cgi-gateway,howbuy-fund-server的所有配置
insert into `zeus_config_instance`
(app_id,
 version,
 env_id,
 effect_env_id,
 `key`,
 value,
 `desc`,
 is_env_tag,
 tag_id,
 old_tag_id,
 prefix,
 old_prefix,
 group_name,
 status,
 assemble_val,
 create_user,
 create_time,
 update_user,
 update_time)
SELECT instance.app_id,
       instance.version,
       1001 AS env_id,
       instance.effect_env_id,
       instance.`key`,
       instance.value,
       instance.`desc`,
       instance.is_env_tag,
       instance.tag_id,
       instance.old_tag_id,
       instance.prefix,
       instance.old_prefix,
       instance.group_name,
       instance.status,
       instance.assemble_val,
       instance.create_user,
       instance.create_time,
       instance.update_user,
       instance.update_time
FROM zeus_config_instance instance
         INNER JOIN zeus_app zapp
                    ON instance.app_id = zapp.id
                        AND zapp.name IN (SELECT distinct bs.module_name
                                          FROM app_deploy_info bs
                                                   LEFT JOIN app_deploy_info tcloud
                                                             ON bs.module_name = tcloud.module_name
                                                                 AND tcloud.suite_code = 'tcloud-prod'
                                          WHERE bs.suite_code = 'bs-prod'
                                            AND bs.module_name NOT IN ('howbuy-fund-server')
                                            AND tcloud.module_name IS NULL
                                          union all
                                          select 'cgi-gateway' as module_name  from dual)
         INNER JOIN zeus_env zenv
                    ON instance.env_id = zenv.id
                        AND zenv.tenant_id = 'bs-prod';


insert into `zeus_config_instance_assemble`
(app_id,
 env_id,
 `key`,
 value,
 is_env_tag,
 tag_id,
 prefix,
 group_name,
 create_user,
 create_time,
 update_user,
 update_time)
SELECT
    assemble_instance.app_id,
    1001 AS env_id,
    assemble_instance.`key`,
    assemble_instance.value,
    assemble_instance.is_env_tag,
    assemble_instance.tag_id,
    assemble_instance.prefix,
    assemble_instance.group_name,
    assemble_instance.create_user,
    assemble_instance.create_time,
    assemble_instance.update_user,
    assemble_instance.update_time
FROM zeus_config_instance_assemble assemble_instance
         INNER JOIN zeus_app zapp
                    ON assemble_instance.app_id = zapp.id
         INNER JOIN zeus_env zenv
                    ON assemble_instance.env_id = zenv.id
                        AND zenv.tenant_id = 'bs-prod'
WHERE zapp.name IN (
    SELECT distinct bs.module_name
    FROM app_deploy_info bs
             LEFT JOIN app_deploy_info tcloud
                       ON bs.module_name = tcloud.module_name
                           AND tcloud.suite_code = 'tcloud-prod'
    WHERE bs.suite_code = 'bs-prod'
      AND bs.module_name NOT IN ( 'howbuy-fund-server')
      AND tcloud.module_name IS NULL
    union all
    select 'cgi-gateway' as module_name  from dual
);


# 查询不包括 cgi-gateway,howbuy-fund-server的所有配置
insert into `zeus_config_instance`
(app_id,
 version,
 env_id,
 effect_env_id,
 `key`,
 value,
 `desc`,
 is_env_tag,
 tag_id,
 old_tag_id,
 prefix,
 old_prefix,
 group_name,
 status,
 assemble_val,
 create_user,
 create_time,
 update_user,
 update_time)
select instance.app_id,
       instance.version,
       1001 AS env_id,
       instance.effect_env_id,
       instance.`key`,
       instance.value,
       instance.`desc`,
       instance.is_env_tag,
       instance.tag_id,
       instance.old_tag_id,
       instance.prefix,
       instance.old_prefix,
       instance.group_name,
       instance.status,
       instance.assemble_val,
       instance.create_user,
       instance.create_time,
       instance.update_user,
       instance.update_time
from zeus_config_instance instance
         INNER JOIN zeus_app zapp ON instance.app_id = zapp.id
         INNER JOIN zeus_env zenv ON instance.env_id = zenv.id
WHERE zenv.tenant_id = 'tcloud-prod'
  AND zapp.name IN ('howbuy-fund-server');

insert into `zeus_config_instance_assemble`
(app_id,
 env_id,
 `key`,
 value,
 is_env_tag,
 tag_id,
 prefix,
 group_name,
 create_user,
 create_time,
 update_user,
 update_time)
select assemble_instance.app_id,
       1001 AS env_id,
       assemble_instance.`key`,
       assemble_instance.value,
       assemble_instance.is_env_tag,
       assemble_instance.tag_id,
       assemble_instance.prefix,
       assemble_instance.group_name,
       assemble_instance.create_user,
       assemble_instance.create_time,
       assemble_instance.update_user,
       assemble_instance.update_time
from zeus_config_instance_assemble assemble_instance
         JOIN
     zeus_app zapp ON assemble_instance.app_id = zapp.id
         JOIN
     zeus_env zenv ON assemble_instance.env_id = zenv.id
WHERE zenv.tenant_id = 'tcloud-prod'
  AND zapp.name IN ('howbuy-fund-server');
# 查询不包括 cgi-gateway,howbuy-fund-server的所有配置
insert into `zeus_config_instance`
(app_id,
 version,
 env_id,
 effect_env_id,
 `key`,
 value,
 `desc`,
 is_env_tag,
 tag_id,
 old_tag_id,
 prefix,
 old_prefix,
 group_name,
 status,
 assemble_val,
 create_user,
 create_time,
 update_user,
 update_time)
SELECT
    instance.app_id,
    instance.version,
    1001 AS env_id,
    instance.effect_env_id,
    instance.`key`,
    instance.value,
    instance.`desc`,
    instance.is_env_tag,
    instance.tag_id,
    instance.old_tag_id,
    instance.prefix,
    instance.old_prefix,
    instance.group_name,
    instance.status,
    instance.assemble_val,
    instance.create_user,
    instance.create_time,
    instance.update_user,
    instance.update_time
FROM zeus_config_instance instance
         INNER JOIN zeus_app zapp
                    ON instance.app_id = zapp.id
                        AND zapp.name IN (
                            SELECT distinct tc.module_name
                            FROM app_deploy_info tc
                                     LEFT JOIN app_deploy_info bs_prod
                                               ON tc.module_name = bs_prod.module_name
                                                   AND bs_prod.suite_code = 'bs-prod'
                            WHERE tc.suite_code = 'tcloud-prod '
                              AND tc.module_name NOT IN ('cgi-gateway', 'howbuy-fund-server')
                              AND bs_prod.module_name IS NULL
                        )
         INNER JOIN zeus_env zenv
                    ON instance.env_id = zenv.id
                        AND zenv.tenant_id = 'tcloud-prod';



insert into `zeus_config_instance_assemble`
(app_id,
 env_id,
 `key`,
 value,
 is_env_tag,
 tag_id,
 prefix,
 group_name,
 create_user,
 create_time,
 update_user,
 update_time)
SELECT
    assemble_instance.app_id,
    1001 AS env_id,
    assemble_instance.`key`,
    assemble_instance.value,
    assemble_instance.is_env_tag,
    assemble_instance.tag_id,
    assemble_instance.prefix,
    assemble_instance.group_name,
    assemble_instance.create_user,
    assemble_instance.create_time,
    assemble_instance.update_user,
    assemble_instance.update_time
FROM zeus_config_instance_assemble assemble_instance
         INNER JOIN zeus_app zapp
                    ON assemble_instance.app_id = zapp.id
         INNER JOIN zeus_env zenv
                    ON assemble_instance.env_id = zenv.id
                        AND zenv.tenant_id = 'tcloud-prod'
WHERE zapp.name IN (
    SELECT distinct tc.module_name
    FROM app_deploy_info tc
             LEFT JOIN app_deploy_info bs_prod
                       ON tc.module_name = bs_prod.module_name
                           AND bs_prod.suite_code = 'bs-prod'
    WHERE tc.suite_code = 'tcloud-prod'
      AND tc.module_name NOT IN ('cgi-gateway', 'howbuy-fund-server')
      AND bs_prod.module_name IS NULL
);

# 查询howbuy-fund-server的所有配置
insert into `zeus_config_instance`
(app_id,
 version,
 env_id,
 effect_env_id,
 `key`,
 value,
 `desc`,
 is_env_tag,
 tag_id,
 old_tag_id,
 prefix,
 old_prefix,
 group_name,
 status,
 assemble_val,
 create_user,
 create_time,
 update_user,
 update_time)
SELECT
    instance.app_id,
    instance.version,
    1002 AS env_id,
    instance.effect_env_id,
    instance.`key`,
    instance.value,
    instance.`desc`,
    instance.is_env_tag,
    instance.tag_id,
    instance.old_tag_id,
    instance.prefix,
    instance.old_prefix,
    instance.group_name,
    instance.status,
    instance.assemble_val,
    instance.create_user,
    instance.create_time,
    instance.update_user,
    instance.update_time
FROM zeus_config_instance instance
         INNER JOIN zeus_app zapp
                    ON instance.app_id = zapp.id
                        AND zapp.name IN ('howbuy-fund-server')
         INNER JOIN zeus_env zenv
                    ON instance.env_id = zenv.id
                        AND zenv.tenant_id = 'bs-prod';


insert into `zeus_config_instance_assemble`
(app_id,
 env_id,
 `key`,
 value,
 is_env_tag,
 tag_id,
 prefix,
 group_name,
 create_user,
 create_time,
 update_user,
 update_time)
SELECT
    assemble_instance.app_id,
    1002 AS env_id,
    assemble_instance.`key`,
    assemble_instance.value,
    assemble_instance.is_env_tag,
    assemble_instance.tag_id,
    assemble_instance.prefix,
    assemble_instance.group_name,
    assemble_instance.create_user,
    assemble_instance.create_time,
    assemble_instance.update_user,
    assemble_instance.update_time
FROM zeus_config_instance_assemble assemble_instance
         INNER JOIN zeus_app zapp
                    ON assemble_instance.app_id = zapp.id
         INNER JOIN zeus_env zenv
                    ON assemble_instance.env_id = zenv.id
                        AND zenv.tenant_id = 'bs-prod'
WHERE zapp.name IN ('howbuy-fund-server');


# 查询不包括 cgi-gateway,howbuy-fund-server的所有配置
insert into `zeus_config_instance`
(app_id,
 version,
 env_id,
 effect_env_id,
 `key`,
 value,
 `desc`,
 is_env_tag,
 tag_id,
 old_tag_id,
 prefix,
 old_prefix,
 group_name,
 status,
 assemble_val,
 create_user,
 create_time,
 update_user,
 update_time)
select instance.app_id,
       instance.version,
       1003 AS env_id,
       instance.effect_env_id,
       instance.`key`,
       instance.value,
       instance.`desc`,
       instance.is_env_tag,
       instance.tag_id,
       instance.old_tag_id,
       instance.prefix,
       instance.old_prefix,
       instance.group_name,
       instance.status,
       instance.assemble_val,
       instance.create_user,
       instance.create_time,
       instance.update_user,
       instance.update_time
from zeus_config_instance instance
         INNER JOIN zeus_app zapp ON instance.app_id = zapp.id
         INNER JOIN zeus_env zenv ON instance.env_id = zenv.id
WHERE zenv.tenant_id = 'pd-prod';

insert into `zeus_config_instance_assemble`
(app_id,
 env_id,
 `key`,
 value,
 is_env_tag,
 tag_id,
 prefix,
 group_name,
 create_user,
 create_time,
 update_user,
 update_time)
select assemble_instance.app_id,
       1003 AS env_id,
       assemble_instance.`key`,
       assemble_instance.value,
       assemble_instance.is_env_tag,
       assemble_instance.tag_id,
       assemble_instance.prefix,
       assemble_instance.group_name,
       assemble_instance.create_user,
       assemble_instance.create_time,
       assemble_instance.update_user,
       assemble_instance.update_time
from zeus_config_instance_assemble assemble_instance
         JOIN
     zeus_app zapp ON assemble_instance.app_id = zapp.id
         JOIN
     zeus_env zenv ON assemble_instance.env_id = zenv.id
WHERE zenv.tenant_id = 'pd-prod';


# 查询的所有配置
insert into `zeus_config_instance`
(app_id,
 version,
 env_id,
 effect_env_id,
 `key`,
 value,
 `desc`,
 is_env_tag,
 tag_id,
 old_tag_id,
 prefix,
 old_prefix,
 group_name,
 status,
 assemble_val,
 create_user,
 create_time,
 update_user,
 update_time)
select instance.app_id,
       instance.version,
       1004 AS env_id,
       instance.effect_env_id,
       instance.`key`,
       instance.value,
       instance.`desc`,
       instance.is_env_tag,
       instance.tag_id,
       instance.old_tag_id,
       instance.prefix,
       instance.old_prefix,
       instance.group_name,
       instance.status,
       instance.assemble_val,
       instance.create_user,
       instance.create_time,
       instance.update_user,
       instance.update_time
from zeus_config_instance instance
         INNER JOIN zeus_app zapp ON instance.app_id = zapp.id
         INNER JOIN zeus_env zenv ON instance.env_id = zenv.id
WHERE
    zapp.name  not in (select DISTINCT module_name from app_deploy_info where suite_code='beta' )
  and
    zenv.tenant_id = 'wgq-zb';

insert into `zeus_config_instance_assemble`
(app_id,
 env_id,
 `key`,
 value,
 is_env_tag,
 tag_id,
 prefix,
 group_name,
 create_user,
 create_time,
 update_user,
 update_time)
select distinct assemble_instance.app_id,
                1004 AS env_id,
                assemble_instance.`key`,
                assemble_instance.value,
                assemble_instance.is_env_tag,
                assemble_instance.tag_id,
                assemble_instance.prefix,
                assemble_instance.group_name,
                assemble_instance.create_user,
                assemble_instance.create_time,
                assemble_instance.update_user,
                assemble_instance.update_time
from zeus_config_instance_assemble assemble_instance
         JOIN
     zeus_app zapp ON assemble_instance.app_id = zapp.id
         JOIN
     zeus_env zenv ON assemble_instance.env_id = zenv.id
WHERE
    zapp.name  not in (select DISTINCT module_name from app_deploy_info where suite_code='beta' )
  and
    zenv.tenant_id = 'wgq-zb';


# 查询的所有配置
insert into `zeus_config_instance`
(app_id,
 version,
 env_id,
 effect_env_id,
 `key`,
 value,
 `desc`,
 is_env_tag,
 tag_id,
 old_tag_id,
 prefix,
 old_prefix,
 group_name,
 status,
 assemble_val,
 create_user,
 create_time,
 update_user,
 update_time)
select instance.app_id,
       instance.version,
       1004 AS env_id,
       instance.effect_env_id,
       instance.`key`,
       instance.value,
       instance.`desc`,
       instance.is_env_tag,
       instance.tag_id,
       instance.old_tag_id,
       instance.prefix,
       instance.old_prefix,
       instance.group_name,
       instance.status,
       instance.assemble_val,
       instance.create_user,
       instance.create_time,
       instance.update_user,
       instance.update_time
from zeus_config_instance instance
         INNER JOIN zeus_app zapp ON instance.app_id = zapp.id
         INNER JOIN zeus_env zenv ON instance.env_id = zenv.id
WHERE
    zapp.name   in (select DISTINCT module_name from app_deploy_info where suite_code='beta' )
  and
    zenv.tenant_id = 'beta';

insert into `zeus_config_instance_assemble`
(app_id,
 env_id,
 `key`,
 value,
 is_env_tag,
 tag_id,
 prefix,
 group_name,
 create_user,
 create_time,
 update_user,
 update_time)
select distinct assemble_instance.app_id,
                1004 AS env_id,
                assemble_instance.`key`,
                assemble_instance.value,
                assemble_instance.is_env_tag,
                assemble_instance.tag_id,
                assemble_instance.prefix,
                assemble_instance.group_name,
                assemble_instance.create_user,
                assemble_instance.create_time,
                assemble_instance.update_user,
                assemble_instance.update_time
from zeus_config_instance_assemble assemble_instance
         JOIN
     zeus_app zapp ON assemble_instance.app_id = zapp.id
         JOIN
     zeus_env zenv ON assemble_instance.env_id = zenv.id
WHERE
    zapp.name   in (select DISTINCT module_name from app_deploy_info where suite_code='beta' )
  and
    zenv.tenant_id = 'beta';






