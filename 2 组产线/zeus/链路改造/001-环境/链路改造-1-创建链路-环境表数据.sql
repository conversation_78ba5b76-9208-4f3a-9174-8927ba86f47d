INSERT INTO zeus_env (id, env_code, env_name, env_status, env_desc, tenant_id, create_user, create_time,
                                 update_user, update_time, stamp, jf_id, room_code, status)
VALUES (1001, 'prod', '外网主链', '使用中', '外网主链（启用中）', 'chain-main', 'howbuyscm', '2025-02-19 01:43:52',
        'howbuyscm', '2025-02-19 01:43:52', null, null, 'bs', 1);

INSERT INTO zeus_env (id, env_code, env_name, env_status, env_desc, tenant_id, create_user, create_time,
                                 update_user, update_time, stamp, jf_id, room_code, status)
VALUES (1002, 'prod', '外网主支链', '使用中', '外网主支链（启用中）', 'chain-main-sub', 'howbuyscm', '2025-02-19 01:43:52',
        'howbuyscm', '2025-02-19 01:43:52', null, null, 'bs', 1);

INSERT INTO zeus_env (id, env_code, env_name, env_status, env_desc, tenant_id, create_user, create_time,
                                 update_user, update_time, stamp, jf_id, room_code, status)
VALUES (1003, 'prod', '内网主链', '使用中', '内网主链（启用中）', 'chain-intra-main', 'howbuyscm', '2025-02-19 01:43:52',
        'howbuyscm', '2025-02-19 01:43:52', null, null, 'hk', 1);

INSERT INTO zeus_env (id, env_code, env_name, env_status, env_desc, tenant_id, create_user, create_time,
                                 update_user, update_time, stamp, jf_id, room_code, status)
VALUES (1004, 'prod', '热备链', '使用中', '热备链（启用中）', 'chain-standby', 'howbuyscm', '2025-02-19 01:43:52',
        'howbuyscm', '2025-02-19 01:43:52', null, null, 'wgq', 1);

INSERT INTO zeus_env (id, env_code, env_name, env_status, env_desc, tenant_id, create_user, create_time,
                                 update_user, update_time, stamp, jf_id, room_code, status)
VALUES (1005, 'prod', '热备支链', '使用中', '热备支链（启用中）', 'chain-standby-sub', 'howbuyscm', '2025-02-19 01:43:52',
        'howbuyscm', '2025-02-19 01:43:52', null, null, 'wgq', 1);


INSERT INTO zeus_env (id, env_code, env_name, env_status, env_desc, tenant_id, create_user, create_time,
                                 update_user, update_time, stamp, jf_id, room_code, status)
VALUES (1006, 'prod', '冷备链', '使用中', '冷备链（启用中）', 'chain-backup', 'howbuyscm', '2025-02-19 01:43:52',
        'howbuyscm', '2025-02-19 01:43:52', null, null, 'wgq', 1);
