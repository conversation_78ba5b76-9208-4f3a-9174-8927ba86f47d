# 创建临时表 -- 对比
create table if not exists `migrate_chain_zeus_env_bak` like `zeus_env`;


# 隐藏环境
select *
from zeus_env
where tenant_id in ("bs-prod", "tcloud-prod", "pd-prod", "wgq-zb","beta");

# update zeus_env
# set status=0
# where tenant_id in ("bs-prod", "tcloud-prod", "pd-prod", "wgq-zb","beta");

# 执行以下 sql隐藏环境
update zeus_env
set status=0
where tenant_id in ("bs-prod", "tcloud-prod", "pd-prod", "wgq-zb","beta","tz-yz","prod","ucloud-prod","hk-prod","bs-zb","it888","zb");




