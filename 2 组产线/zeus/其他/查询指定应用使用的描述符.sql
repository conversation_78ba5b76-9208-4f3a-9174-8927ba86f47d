select distinct zci.tag_id
from zeus_config_instance zci
         INNER JOIN zeus_env zenv ON zci.env_id = zenv.id
  INNER JOIN zeus_app zapp on zapp.id = zci.app_id
  INNER JOIN app_info_devops aid on aid.app_name = zapp.name
where zenv.tenant_id = 'tcloud-prod'
  and zci.tag_id is not null
  and zci.is_env_tag=1
  and zapp.name = 'howbuy-fund-server'
  and zci.version = aid.last_archived;


select zci.*
from zeus_config_instance zci
         INNER JOIN zeus_env zenv ON zci.env_id = zenv.id
  INNER JOIN zeus_app zapp on zapp.id = zci.app_id
  INNER JOIN app_info_devops aid on aid.app_name = zapp.name
where zenv.tenant_id = 'bs-prod'
  and zci.tag_id is not null
  and zci.is_env_tag=1
  and zapp.name = 'howbuy-fund-server'
  and zci.version = aid.last_archived;



select distinct zapp.name,zenv.tenant_id,`key`,JSON_EXTRACT(`value`, '$.needLocalCache')
from zeus_config_instance zci
         INNER JOIN zeus_env zenv ON zci.env_id = zenv.id
  INNER JOIN zeus_app zapp on zapp.id = zci.app_id
  INNER JOIN app_info_devops aid on aid.app_name = zapp.name
  INNER JOIN app_deploy_info adi on adi.module_name = zapp.name and adi.suite_code=zenv.tenant_id
where zci.tag_id =1710529381585
  and zenv.env_code='prod'
  and zci.is_env_tag=1
  and zci.version = aid.last_archived;




select zci.*
from zeus_config_instance zci
         INNER JOIN zeus_env zenv ON zci.env_id = zenv.id
  INNER JOIN zeus_app zapp on zapp.id = zci.app_id
  INNER JOIN app_info_devops aid on aid.app_name = zapp.name
where zenv.tenant_id = 'beta'
  and zci.tag_id is not null
  and zci.is_env_tag=1
  and zapp.name in (select DISTINCT module_name from app_deploy_info where suite_code='beta')
  and zci.version = aid.last_archived;



select zci.tag_id
from zeus_config_instance zci
         INNER JOIN zeus_env zenv ON zci.env_id = zenv.id
  INNER JOIN zeus_app zapp on zapp.id = zci.app_id
  INNER JOIN app_info_devops aid on aid.app_name = zapp.name
where zenv.tenant_id = 'wgq-zb'
  and zci.tag_id is not null
  and zci.is_env_tag=1
  and zapp.name in (select DISTINCT module_name from app_deploy_info where suite_code='beta')
  and zci.version = aid.last_archived;


# 查询两个产线环境的描述符值不一致的描述符
SELECT bs.tag_id,
       CONCAT(s1.code, '_', zt.second_code, '_', s3.code) AS '描述符',
       bs.et_val                                          AS 'bs-prod',
       tcloud.et_val                                      AS 'tcloud-prod'
FROM zeus_tag_val bs
         INNER JOIN zeus_env be ON bs.env_id = be.id AND be.tenant_id = 'wgq-zb'
         INNER JOIN zeus_tag_val tcloud
                    ON bs.tag_id = tcloud.tag_id
                        AND tcloud.env_id = (SELECT id FROM zeus_env WHERE tenant_id = 'beta' LIMIT 1)
         INNER JOIN zeus_tag zt ON bs.tag_id = zt.id
         LEFT JOIN zeus_tag_segment_dict s1 ON zt.first_code = s1.id
         LEFT JOIN zeus_tag_segment_dict s3 ON zt.third_code = s3.id
WHERE bs.et_val != tcloud.et_val
  and bs.tag_id in (select zci.tag_id
                    from zeus_config_instance zci
                             join zeus_app zpp on zci.app_id = zpp.id
                             join app_info_devops on zpp.name = app_info_devops.app_name
                    where zci.version = app_info_devops.last_archived
                      and zci.is_env_tag = 1);
order by `描述符`;




select distinct app_id,be.tenant_id from zeus_config_instance
    INNER JOIN zeus_env be ON zeus_config_instance.env_id = be.id and be.tenant_id ='beta';

SELECT
    CONCAT(s1.code, '_', zt.second_code, '_', s3.code) AS '描述符',
    bs.et_val AS 'bs-prod',
    tcloud.et_val AS 'tcloud-prod'
FROM zeus_tag_val bs
INNER JOIN zeus_env be ON bs.env_id = be.id AND be.tenant_id = 'wgq-zb'
INNER JOIN zeus_tag_val tcloud
    ON bs.tag_id = tcloud.tag_id
    AND tcloud.env_id = (SELECT id FROM zeus_env WHERE tenant_id = 'beta' LIMIT 1)
INNER JOIN zeus_tag zt ON bs.tag_id = zt.id
LEFT JOIN zeus_tag_segment_dict s1 ON zt.first_code = s1.id
LEFT JOIN zeus_tag_segment_dict s3 ON zt.third_code = s3.id
WHERE bs.et_val != tcloud.et_val;