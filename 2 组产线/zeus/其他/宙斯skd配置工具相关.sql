delete from sdk_config_add_snapshot;
delete from sdk_config_replace_app_deploy_info ;
delete from sdk_config_replace_app_key_mapping ;
delete from sdk_config_replace_snapshot;




INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('crm-core-server', 'prod', '1.7.3', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('crm-core-server', 'bs-zb', '1.7.3', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('crm-hb-webapp', 'prod', '1.7.3', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('crm-hb-webapp', 'bs-zb', '1.7.3', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('crm-mobile-webapp', 'prod', '1.7.3', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('crm-mobile-webapp', 'bs-zb', '1.7.3', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('crm-nt-server', 'prod', '1.7.3', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('crm-nt-server', 'bs-zb', '1.7.3', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('crm-sys-webapp', 'prod', '1.7.5.3', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('crm-sys-webapp', 'bs-zb', '1.7.5.3', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('crm-td-server', 'prod', '1.7.3', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('crm-td-server', 'bs-zb', '1.7.3', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('crm-wechat-remote', 'prod', '1.7.3.5', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('cs-task-server', 'prod', '1.7.3', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('cs-webapp', 'prod', '1.7.5.3', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('cs-webapp', 'bs-zb', '1.7.5.3', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('hb-wechat-server', 'prod', '1.7.3.5', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('hbdoc-webapp', 'prod', 'bugfix20240204', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('hbdoc-webapp', 'bs-zb', 'bugfix20240204', 'addConfig');
INSERT INTO sdk_config_replace_app_deploy_info (app_name, tenant_id, version, type) VALUES ('howbuy-crm-sync', 'prod', 'bugfix-20240328', 'addConfig');

update sdk_config_replace_app_deploy_info set snapshot_num='2024040701';

select * from sdk_config_replace_app_deploy_info;

INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value) VALUES ('upgrade', 'crm-core-server', 'CRM_REDIS_CONFIG', 'CRM_REDIS6_CONFIG');
INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value) VALUES ('upgrade', 'crm-hb-webapp', 'CRM_REDIS_CONFIG', 'CRM_REDIS6_CONFIG');
INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value) VALUES ('upgrade', 'crm-mobile-webapp', 'CRM_REDIS_CONFIG', 'CRM_REDIS6_CONFIG');
INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value) VALUES ('upgrade', 'crm-nt-server', 'CRM_REDIS_CONFIG', 'CRM_REDIS6_CONFIG');
INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value) VALUES ('upgrade', 'crm-sys-webapp', 'CRM_REDIS_CONFIG', 'CRM_REDIS6_CONFIG');
INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value) VALUES ('upgrade', 'crm-td-server', 'CRM_REDIS_CONFIG', 'CRM_REDIS6_CONFIG');
INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value) VALUES ('upgrade', 'crm-wechat-remote', 'CRM_REDIS_CONFIG', 'CRM_REDIS6_CONFIG');
INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value) VALUES ('upgrade', 'cs-task-server', 'CRM_REDIS_CONFIG', 'CRM_REDIS6_CONFIG');
INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value) VALUES ('upgrade', 'cs-webapp', 'CRM_REDIS_CONFIG', 'CRM_REDIS6_CONFIG');
INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value) VALUES ('upgrade', 'hb-wechat-server', 'CRM_REDIS_CONFIG', 'CRM_REDIS6_CONFIG');
INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value) VALUES ('upgrade', 'hbdoc-webapp', 'CRM_REDIS_CONFIG', 'CRM_REDIS6_CONFIG');
INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value) VALUES ('upgrade', 'howbuy-crm-sync', 'CRM_REDIS_CONFIG', 'CRM_REDIS6_CONFIG');

select * from sdk_config_replace_app_key_mapping;
select * from sdk_config_add_snapshot;
select * from sdk_config_replace_snapshot;


select * from zeus_master_config where  `key` = 'CRM_REDIS_CONFIG';









SELECT DISTINCT app.name, deploy.version AS version, deploy.tenant_id AS tenant_id, instance.`key`, instance.`value`, instance.group_name, instance.status
FROM zeus_config_instance instance
JOIN zeus_env env ON instance.env_id = env.id
JOIN zeus_app app ON app.id = instance.app_id
RIGHT JOIN (
  SELECT app_name, version, tenant_id
  FROM sdk_config_replace_app_deploy_info
) deploy ON deploy.app_name = app.name AND deploy.tenant_id = env.tenant_id
JOIN sdk_config_replace_app_deploy_info deploy_br ON deploy_br.app_name = app.name AND deploy_br.version = instance.version
WHERE instance.`key` = 'CRM_REDIS6_CONFIG';

# count =20


SELECT DISTINCT app.name, deploy.version AS version, deploy.tenant_id AS tenant_id, instance.`key`, instance.`value`, instance.group_name, instance.status
FROM zeus_config_instance instance
JOIN zeus_env env ON instance.env_id = env.id
JOIN zeus_app app ON app.id = instance.app_id
RIGHT JOIN (
  SELECT app_name, version, tenant_id
  FROM sdk_config_replace_app_deploy_info
) deploy ON deploy.app_name = app.name AND deploy.tenant_id = env.tenant_id
JOIN sdk_config_replace_app_deploy_info deploy_br ON deploy_br.app_name = app.name AND deploy_br.version = instance.version
WHERE instance.`value` LIKE '%CRM_REDIS6_CONFIG%';

# count =63


select