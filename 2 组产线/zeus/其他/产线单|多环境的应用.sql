# 查询只有 bs-prod的应用
select distinct adi.module_name
from app_deploy_info adi
where adi.suite_code = 'bs-prod'
  AND adi.module_name NOT IN ('cgi-gateway', 'howbuy-fund-server')
  and adi.module_name NOT IN (select distinct adi.module_name
                              from app_deploy_info adi
                              where adi.suite_code = 'tcloud-prod'
                                AND adi.module_name NOT IN ('cgi-gateway', 'howbuy-fund-server'));

# 查询只有 tcloud-prod的应用
select distinct adi.module_name
from app_deploy_info adi
where adi.suite_code = 'tcloud-prod'
  AND adi.module_name NOT IN ('cgi-gateway', 'howbuy-fund-server')
  and adi.module_name NOT IN (select distinct adi.module_name
                              from app_deploy_info adi
                              where adi.suite_code = 'bs-prod'
                                AND adi.module_name NOT IN ('cgi-gateway', 'howbuy-fund-server'));


# 查询同时有 bs-prod,tcloud-prod的应用
select distinct adi.module_name
from app_deploy_info adi
where adi.suite_code = 'tcloud-prod'
  and adi.module_name IN (select distinct adi.module_name
                          from app_deploy_info adi
                          where adi.suite_code = 'bs-prod');