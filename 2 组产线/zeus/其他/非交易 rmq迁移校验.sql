# 查询指定topic的应用是否正确
select distinct app.name,
                instance.app_id,
                instance.version,
                env.env_name,
                instance.`key`,
                instance.value,
                JSON_EXTRACT(instance.value, '$.order')     as orderTopic,
                JSON_EXTRACT(instance.value, '$.msgServer') as msgSever,
                env.id                                      as env_id
from zeus_config_instance instance
         left join zeus_app app on instance.app_id = app.id
         left join zeus_env env on instance.env_id = env.id
         join (select app_name, IFNULL(last_deployed, last_archived) onLineVersion from app_info_devops) aid
              on aid.app_name = app.name and aid.onLineVersion = instance.version
where instance.`key` in ('ams_jhc_jd_data_callback_msg',
                         'ams_report_push_cms',
                         'es_simu_yxs_search_flush',
                         'sm_yxs_audition_flush_task',
                         'fpc-manage-console_alarmTopic',
                         'fpc-manage-report_auditTopic',
                         'fpc_flushDSTopic'
    )
  and JSON_VALID(instance.value)
  and env.env_code = 'prod'
  and env.env_name not in ("浦东内网生产环境", "唐镇验证", "虹口内网生产环境", "外高桥生产环境")
  and app.name not in ('')
order by instance.`key`, env.env_name;


# 查询指定topic的应用,版本，环境
select distinct app.name,
                instance.app_id,
                instance.version,
                env.env_name,
                env.id as env_id
from zeus_config_instance instance
         left join zeus_app app on instance.app_id = app.id
         left join zeus_env env on instance.env_id = env.id
         join (select app_name, IFNULL(last_deployed, last_archived) onLineVersion from app_info_devops) aid
              on aid.app_name = app.name and aid.onLineVersion = instance.version
where instance.`key` in (select distinct topicName
                         from rmq_migrate_pqc_canal)
  and JSON_VALID(instance.value)
  and env.env_code = 'prod'
  and env.env_name not in ("浦东内网生产环境", "唐镇验证", "虹口内网生产环境", "外高桥生产环境")

union ALL

select distinct app.name,
                instance.app_id,
                instance.version,
                env.env_name,
                env.id as env_id
from zeus_config_instance instance
         left join zeus_app app on instance.app_id = app.id
         left join zeus_env env on instance.env_id = env.id
         join (select app_name, IFNULL(last_deployed, last_archived) onLineVersion from app_info_devops) aid
              on aid.app_name = app.name and aid.onLineVersion = instance.version
where (instance.value like "%canalmqns%" or instance.value like "%10.11.54.66%" or instance.value like "%10.11.54.121%")
  and env.env_code = 'prod'
  and env.env_name not in ("浦东内网生产环境", "唐镇验证", "虹口内网生产环境", "外高桥生产环境")
order by env_name;


# 查询指定topic的应用,版本，环境
select distinct app.name,
                instance.app_id,
                env.env_name,
                env.id as env_id
from zeus_config_instance_assemble instance
         left join zeus_app app on instance.app_id = app.id
         left join zeus_env env on instance.env_id = env.id
         join (select app_name, IFNULL(last_deployed, last_archived) onLineVersion from app_info_devops) aid
              on aid.app_name = app.name
where instance.`key` in (select distinct topicName
                         from rmq_migrate_pqc_canal)
  and JSON_VALID(instance.value)
  and env.env_code = 'prod'
  and env.env_name not in ("浦东内网生产环境", "唐镇验证", "虹口内网生产环境", "外高桥生产环境")
union ALL
select distinct app.name,
                instance.app_id,
                env.env_name,
                env.id as env_id
from zeus_config_instance_assemble instance
         left join zeus_app app on instance.app_id = app.id
         left join zeus_env env on instance.env_id = env.id
         join (select app_name, IFNULL(last_deployed, last_archived) onLineVersion from app_info_devops) aid
              on aid.app_name = app.name
where (instance.value like "%canalmqns%" or instance.value like "%10.11.54.66%" or instance.value like "%10.11.54.121%")
  and not JSON_VALID(instance.value)
  and env.env_code = 'prod'
  and env.env_name not in ("浦东内网生产环境", "唐镇验证", "虹口内网生产环境", "外高桥生产环境")
order by env_name;

# 查询指定topic的 msgServer是否正确
select *
from (select distinct instance.*,
                      JSON_EXTRACT(instance_server.value, '$.urls') as urls,
                      JSON_EXTRACT(instance_server.value, '$.type')
      from (select distinct app.name,
                            instance.app_id,
                            instance.version,
                            env.env_name,
                            instance.`key`,
                            instance.value,
                            JSON_EXTRACT(instance.value, '$.msgServer') as msgSever,
                            env.id                                      as env_id
            from zeus_config_instance instance
                     left join zeus_app app on instance.app_id = app.id
                     left join zeus_env env on instance.env_id = env.id
                     join (select app_name, IFNULL(last_deployed, last_archived) onLineVersion from app_info_devops) aid
                          on aid.app_name = app.name and aid.onLineVersion = instance.version
            where instance.`key` in ('fpc_das_fof_reptile_ams',
                                     'queue_das_ams_zdyzhcp_hd',
                                     'queue_das_crm',
                                     'queue_das_crm_jjjz',
                                     'queue_das_fof',
                                     'queue_das_fps_high',
                                     'queue_das_fps_high_ams',
                                     'queue_das_fps_rhb_fund',
                                     'queue_das_tms_asset',
                                     'queue_das_tms_asset_jjjz',
                                     'queue_das_tp'
                )
              and JSON_VALID(instance.value)
              and env.env_code = 'prod'
              and env.env_name not in ("浦东内网生产环境", "唐镇验证", "虹口内网生产环境", "外高桥生产环境")
            order by env.env_name) instance
               left join zeus_config_instance instance_server
                         on instance.msgSever = instance_server.`key` and instance.env_id = instance_server.env_id and
                            instance.app_id = instance_server.app_id
                             and instance.version = instance_server.version
      order by instance.name, instance.`key`) instance
where instance.urls != 'mq1ns-1.inner.howbuy.com:9876;mq2ns-1.inner.howbuy.com:9876;mq3ns-1.inner.howbuy.com:9876'


# 查询msgServer的值是指定值的 topic有哪些是否正确
select distinct *
from (select distinct instance.*,
                      JSON_EXTRACT(instance_server.value, '$.urls') as urls,
                      JSON_EXTRACT(instance_server.value, '$.type')
      from (select distinct app.name,
                            instance.app_id,
                            instance.version,
                            env.env_name,
                            instance.`key`,
                            instance.value,
                            JSON_EXTRACT(instance.value, '$.msgServer') as msgSever,
                            env.id                                      as env_id
            from zeus_config_instance instance
                     left join zeus_app app on instance.app_id = app.id
                     left join zeus_env env on instance.env_id = env.id
                     join (select app_name, IFNULL(last_deployed, last_archived) onLineVersion from app_info_devops) aid
                          on aid.app_name = app.name and aid.onLineVersion = instance.version
                              and instance.group_name = "msgConfig"
                              and JSON_VALID(instance.value)
                              and env.env_code = 'prod'
                              and
                             env.env_name not in ("浦东内网生产环境", "唐镇验证", "虹口内网生产环境", "外高桥生产环境")
            order by env.env_name) instance
               left join zeus_config_instance instance_server
                         on instance.msgSever = instance_server.`key` and instance.env_id = instance_server.env_id and
                            instance.app_id = instance_server.app_id
                             and instance.version = instance_server.version
      where JSON_VALID(instance_server.value)
      order by instance.name, instance.`key`) instance
where instance.urls like "%mainmqns%"
   or instance.urls like "%***********%"
   or instance.urls like "%************%"
   or instance.urls like "%rmq1-server.inner.howbuy.com%"
   or instance.urls like "%rmq2-server.inner.howbuy.com%";
# where instance.urls like "%canalmqns%" or instance.urls like "%10.11.54.66%" or instance.urls like "%10.11.54.121%" ;
#
# 新值:mq1ns-1.inner.howbuy.com:9876;mq2ns-1.inner.howbuy.com:9876;mq3ns-1.inner.howbuy.com:9876
# 旧值:mainmqns-1.inner.howbuy.com:9876;mainmqns-2.inner.howbuy.com:9876
# 旧值:rmq1-server.inner.howbuy.com:9876;rmq2-server.inner.howbuy.com:9876
# 旧值:canalmqns-1.inner.howbuy.com:9876;canalmqns-2.inner.howbuy.com:9876

# 校验 nacos是否有差异


# 宙斯有消息的配置，但是rmq没有消费者的主题
select distinct zeus_topic.name,zeus_topic.`key`
from (select distinct *
      from (select distinct instance.*,
                            JSON_EXTRACT(instance_server.value, '$.urls') as urls,
                            JSON_EXTRACT(instance_server.value, '$.type')
            from (select distinct app.name,
                                  instance.app_id,
                                  instance.version,
                                  env.env_name,
                                  instance.`key`,
                                  instance.value,
                                  JSON_EXTRACT(instance.value, '$.msgServer') as msgSever,
                                  env.id                                      as env_id
                  from zeus_config_instance instance
                           left join zeus_app app on instance.app_id = app.id
                           left join zeus_env env on instance.env_id = env.id
                           join (select app_name, IFNULL(last_deployed, last_archived) onLineVersion
                                 from app_info_devops) aid
                                on aid.app_name = app.name and aid.onLineVersion = instance.version
                                    and instance.group_name = "msgConfig"
                                    and JSON_VALID(instance.value)
                                    and env.env_code = 'prod'
                                    and env.env_name not in
                                        ("浦东内网生产环境", "唐镇验证", "虹口内网生产环境", "外高桥生产环境")
                  order by env.env_name) instance
                     left join zeus_config_instance instance_server
                               on instance.msgSever = instance_server.`key` and
                                  instance.env_id = instance_server.env_id and
                                  instance.app_id = instance_server.app_id
                                   and instance.version = instance_server.version
            where JSON_VALID(instance_server.value)
            order by instance.name, instance.`key`) instance
      where  instance.urls like "%***********%"
         or instance.urls like "%************%"
         or instance.urls like "%rmq1-server.inner.howbuy.com%"
         or instance.urls like "%rmq2-server.inner.howbuy.com%"
         or instance.urls like "%rmq3-server.inner.howbuy.com%"
         or instance.urls like "%rmq4-server.inner.howbuy.com%"
         or instance.urls like "%mainmqns-1.inner.howbuy.com%"
         or instance.urls like "%mainmqns-2.inner.howbuy.com%") zeus_topic
         left join rmq_migrate_pqc on zeus_topic.`key` = rmq_migrate_pqc.topicName
where rmq_migrate_pqc.topicName is null;


select distinct topicName, groupName
from (select * from rmq_migrate_pqc where appName != "not online") temp
where topicName not in
      (select distinct topicName from rmq_migrate_pqc where relationShip = "producer" and appName = "not online")
  and relationShip = "consumer"
order by topicName, groupName;



select distinct topicName
from rmq_migrate_pqc
where relationShip = "producer"
order by topicName;

select distinct topicName
from rmq_migrate_pqc
where relationShip = "consumer"
order by topicName;

# 找到没有生产者的 topic和消费者组名
select distinct *
from rmq_migrate_pqc
where relationShip = "consumer"
  and topicName not in (select distinct topicName
                        from rmq_migrate_pqc
                        where relationShip = "producer")
order by messageModel, consumeType, topicName;



select distinct zeus_topic.name,
                zeus_topic.`key`,
                replace(JSON_EXTRACT(zeus_topic.value, "$.groupName"), '"', "") as groupName
from (select distinct *
      from (select distinct instance.*,
                            JSON_EXTRACT(instance_server.value, '$.urls') as urls,
                            JSON_EXTRACT(instance_server.value, '$.type')
            from (select distinct app.name,
                                  instance.app_id,
                                  instance.version,
                                  env.env_name,
                                  instance.`key`,
                                  instance.value,
                                  JSON_EXTRACT(instance.value, '$.msgServer') as msgSever,
                                  env.id                                      as env_id
                  from zeus_config_instance instance
                           left join zeus_app app on instance.app_id = app.id
                           left join zeus_env env on instance.env_id = env.id
                           join (select app_name, IFNULL(last_deployed, last_archived) onLineVersion
                                 from app_info_devops) aid
                                on aid.app_name = app.name and aid.onLineVersion = instance.version
                                    and instance.group_name = "msgConfig"
                                    and JSON_VALID(instance.value)
                                    and env.env_code = 'prod'
                                    and env.env_name not in
                                        ("浦东内网生产环境", "唐镇验证", "虹口内网生产环境", "外高桥生产环境")
                  order by env.env_name) instance
                     left join zeus_config_instance instance_server
                               on instance.msgSever = instance_server.`key` and
                                  instance.env_id = instance_server.env_id and
                                  instance.app_id = instance_server.app_id
                                   and instance.version = instance_server.version
            where JSON_VALID(instance_server.value)
            order by instance.name, instance.`key`) instance
      where instance.urls like "%mainmqns%"
         or instance.urls like "%***********%"
         or instance.urls like "%************%"
         or instance.urls like "%rmq1-server.inner.howbuy.com%"
         or instance.urls like "%rmq2-server.inner.howbuy.com%") zeus_topic
         join (select distinct *
               from rmq_migrate_pqc
               where relationShip = "consumer"
                 and topicName not in (select distinct topicName
                                       from rmq_migrate_pqc
                                       where relationShip = "producer")) pqc on zeus_topic.`key` = pqc.topicName
where groupName not in (select distinct pqc.groupName
                        from rmq_migrate_pqc
                        where relationShip = "consumer"
                          and topicName not in (select distinct topicName
                                                from rmq_migrate_pqc
                                                where relationShip = "producer"))
order by zeus_topic.`key`;


# 所有应用名
select distinct appName
from rmq_migrate_pqc;

select *
from rmq_migrate_pqc
where relationShip = "producer"
  and topicName = "ams_report_push_cms";
select *
from rmq_migrate_pqc
where relationShip = "consumer"
  and topicName = "ams_jhc_jd_data_callback_msg_Group";



select zeus_topic.*
from (select distinct *
      from (select distinct instance.*,
                            JSON_EXTRACT(instance_server.value, '$.urls') as urls,
                            JSON_EXTRACT(instance_server.value, '$.type')
            from (select distinct app.name,
                                  instance.app_id,
                                  instance.version,
                                  env.env_name,
                                  instance.`key`,
                                  instance.value,
                                  JSON_EXTRACT(instance.value, '$.msgServer') as msgSever,
                                  env.id                                      as env_id
                  from zeus_config_instance instance
                           left join zeus_app app on instance.app_id = app.id
                           left join zeus_env env on instance.env_id = env.id
                           join (select app_name, IFNULL(last_deployed, last_archived) onLineVersion
                                 from app_info_devops) aid
                                on aid.app_name = app.name and aid.onLineVersion = instance.version
                                    and instance.group_name = "msgConfig"
                                    and JSON_VALID(instance.value)
                                    and env.env_code = 'prod'
                                    and env.env_name not in
                                        ("浦东内网生产环境", "唐镇验证", "虹口内网生产环境", "外高桥生产环境")
                  order by env.env_name) instance
                     left join zeus_config_instance instance_server
                               on instance.msgSever = instance_server.`key` and
                                  instance.env_id = instance_server.env_id and
                                  instance.app_id = instance_server.app_id
                                   and instance.version = instance_server.version
            where JSON_VALID(instance_server.value)
            order by instance.name, instance.`key`) instance
      where instance.urls like "%canalmqns%"
         or instance.urls like "%10.11.54.66%"
         or instance.urls like "%10.11.54.121%") zeus_topic
         left join rmq_migrate_pqc_canal on zeus_topic.`key` = rmq_migrate_pqc_canal.topicName
where rmq_migrate_pqc_canal.topicName is null;


