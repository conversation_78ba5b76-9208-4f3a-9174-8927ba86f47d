# 1 10.12.60.69
# 2 10.12.60.83
# 3 10.11.54.143 web1-redis.inner.howbuy.com
# 4 10.11.54.192 fund-redis.inner.howbuy.com
# 5 10.12.60.56
# 6 10.12.60.50
# 7 10.12.60.54

# 查询影响的应用的 config
select  app.name, `key`, instance.*
               from zeus_config_instance instance
                        join zeus_app app on app.id = instance.app_id
                        join zeus_env env on env.id = instance.env_id
  join zeus_app_module module on module.br_name=instance.version and  module.module_name = app.name
               where instance.value like "%10.12.60.50%"
                 and env.env_code = "prod";


# 查看影响的key值
select distinct  app.name, `key`,instance.version, JSON_EXTRACT(instance.value, '$.addrList') AS key_value
               from zeus_config_instance instance
                        join zeus_app app on app.id = instance.app_id
                        join zeus_env env on env.id = instance.env_id
  join zeus_app_module module on module.br_name=instance.version and  module.module_name = app.name
               where instance.value like "%10.12.60.69%"
                 and env.env_code = "prod";

#查看影响的key值 & config
select distinct app.name,c_instance.`key`,c_instance.value,instance.`key` as config_key,instance.value as config_value,instance.tag_id as config_tag_id
from zeus_config_instance c_instance
         join (select distinct app.name, instance.app_id, `key`, instance.tag_id, instance.value
               from zeus_config_instance instance
                        join zeus_app app on app.id = instance.app_id
                        join zeus_env env on env.id = instance.env_id
               join zeus_app_module module on module.br_name=instance.version and  module.module_name = app.name
               where instance.value like "%10.12.60.54%"
                 and env.env_code = "prod") instance on c_instance.value like concat('%', instance.`key`, '%')
    and c_instance.app_id = instance.app_id
join zeus_app app on app.id = c_instance.app_id
join zeus_env env on env.id = c_instance.env_id and env.env_code = "prod"
join zeus_app_module module on module.br_name=c_instance.version and  module.module_name = app.name;


# 去重影响的 key
select distinct app.name,c_instance.`key`
from zeus_config_instance c_instance
         join (select distinct app.name, instance.app_id, `key`, instance.tag_id, instance.value
               from zeus_config_instance instance
                        join zeus_app app on app.id = instance.app_id
                        join zeus_env env on env.id = instance.env_id
               join zeus_app_module module on module.br_name=instance.version and  module.module_name = app.name
               where instance.value like "%fund-redis.inner.howbuy.com%"
                 and env.env_code = "prod") instance on c_instance.value like concat('%', instance.`key`, '%')
    and c_instance.app_id = instance.app_id
join zeus_app app on app.id = c_instance.app_id
join zeus_env env on env.id = c_instance.env_id and env.env_code = "prod"
join zeus_app_module module on module.br_name=c_instance.version and  module.module_name = app.name
;
# 去重影响的应用
select distinct app.name
from zeus_config_instance c_instance
         join (select distinct app.name, instance.app_id, `key`, instance.tag_id, instance.value
               from zeus_config_instance instance
                        join zeus_app app on app.id = instance.app_id
                        join zeus_env env on env.id = instance.env_id
                join zeus_app_module module on module.br_name=instance.version and  module.module_name = app.name
               where instance.value like "%10.12.60.83%"
                 and env.env_code = "prod") instance on c_instance.value like concat('%', instance.`key`, '%')
    and c_instance.app_id = instance.app_id
join zeus_app app on app.id = c_instance.app_id
join zeus_env env on env.id = c_instance.env_id and env.env_code = "prod"
join zeus_app_module module on module.br_name=c_instance.version and  module.module_name = app.name;



# 查看需要替换的key原值
select distinct  app.name, `key`,instance.version, JSON_EXTRACT(instance.value, '$.addrList') AS key_value
               from zeus_config_instance instance
                        join zeus_app app on app.id = instance.app_id
                        join zeus_env env on env.id = instance.env_id
  join zeus_app_module module on module.br_name=instance.version and  module.module_name = app.name
               where instance.value like "%10.12.60.53:26379%"
                 and env.env_code = "prod";



select  *  from  zeus_config_instance_assemble  where app_id=328 and env_id=99;


select * from sdk_config_replace_app_deploy_info where app_name='pension-order-remote';
