# 查询两个产线环境的描述符值不一致的描述符
SELECT
    bs.tag_id,
    CONCAT(s1.code, '_', zt.second_code, '_', s3.code) AS '描述符',
    bs.et_val AS 'bs-prod',
    tcloud.et_val AS 'tcloud-prod'
FROM zeus_tag_val bs
INNER JOIN zeus_env be ON bs.env_id = be.id AND be.tenant_id = 'bs-prod'
INNER JOIN zeus_tag_val tcloud
    ON bs.tag_id = tcloud.tag_id
    AND tcloud.env_id = (SELECT id FROM zeus_env WHERE tenant_id = 'tcloud-prod' LIMIT 1)
INNER JOIN zeus_tag zt ON bs.tag_id = zt.id
LEFT JOIN zeus_tag_segment_dict s1 ON zt.first_code = s1.id
LEFT JOIN zeus_tag_segment_dict s3 ON zt.third_code = s3.id
WHERE bs.et_val != tcloud.et_val
and bs.tag_id in (select zci.tag_id from zeus_config_instance zci
 join zeus_app zpp on zci.app_id=zpp.id
join app_info_devops on zpp.name=app_info_devops.app_name
where zci.version =app_info_devops.last_archived
and zci.is_env_tag = 1);
order by `描述符`;

# 查询两个产线环境的描述符值不一致的描述符,且有人用的
select zci.tag_id from zeus_config_instance zci
 join zeus_app zpp on zci.app_id=zpp.id
join app_info_devops on zpp.name=app_info_devops.app_name
where zci.version =app_info_devops.last_archived
and zci.is_env_tag = 1;


# 查询两个产线环境的描述符值不一致的描述符,以及使用这个描述符的app
select group_concat(distinct zpp.name),tzv.tag_id,tzv.`描述符`,tzv.`bs-prod`,tzv.`tcloud-prod` from zeus_config_instance zci
 join zeus_app zpp on zci.app_id=zpp.id
 join zeus_env zenv on zenv.id=zci.env_id
join app_info_devops on zpp.name=app_info_devops.app_name
         join (    SELECT
    bs.tag_id,
    CONCAT(s1.code, '_', zt.second_code, '_', s3.code) AS '描述符',
    bs.et_val AS 'bs-prod',
    tcloud.et_val AS 'tcloud-prod'
FROM zeus_tag_val bs
INNER JOIN zeus_env be ON bs.env_id = be.id AND be.tenant_id = 'bs-prod'
INNER JOIN zeus_tag_val tcloud
    ON bs.tag_id = tcloud.tag_id
    AND tcloud.env_id = (SELECT id FROM zeus_env WHERE tenant_id = 'tcloud-prod' LIMIT 1)
INNER JOIN zeus_tag zt ON bs.tag_id = zt.id
LEFT JOIN zeus_tag_segment_dict s1 ON zt.first_code = s1.id
LEFT JOIN zeus_tag_segment_dict s3 ON zt.third_code = s3.id
WHERE bs.et_val != tcloud.et_val) tzv on zci.tag_id=tzv.tag_id
where zci.version =app_info_devops.last_archived
group by tzv.tag_id,tzv.`描述符`,tzv.`bs-prod`,tzv.`tcloud-prod`
order by zpp.name;

