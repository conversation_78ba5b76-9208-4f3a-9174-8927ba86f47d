-- 查询在不不止一个产线环境发布过配置的应用
SELECT
    app.`name`,
    GROUP_CONCAT(DISTINCT env.tenant_id) AS deployed_environments,
    COUNT(DISTINCT env.tenant_id) AS prod_environment_count
FROM
    zeus_config_instance_assemble instance_assemble
JOIN
    zeus_app app ON instance_assemble.app_id = app.id
JOIN
    zeus_env env ON instance_assemble.env_id = env.id
JOIN
    app_deploy_info app_env_node
    ON app.name = app_env_node.module_name
    AND env.tenant_id = app_env_node.suite_code
WHERE
    env.env_code = 'prod'
    AND env.tenant_id IN ('pd-prod', 'bs-prod', 'tcloud-prod')
GROUP BY
    app.`name`
HAVING
    prod_environment_count > 1;


# 查询有多个产线环境的应用,但在宙斯没发过配置的应用环境
select distinct app_env_mult_node.`module_name`,app_env_mult_node.suite_code,instance_assemble.env_id
FROM
    zeus_config_instance_assemble instance_assemble
JOIN
    zeus_app app ON instance_assemble.app_id = app.id
JOIN
    zeus_env env ON instance_assemble.env_id = env.id
right JOIN
    (
        SELECT
    module_name,
    suite_code
FROM app_deploy_info
WHERE module_name IN (
    SELECT module_name
    FROM app_deploy_info
    GROUP BY module_name
    HAVING COUNT(DISTINCT suite_code) > 1
)
GROUP BY module_name, suite_code
    ) app_env_mult_node ON app.name = app_env_mult_node.module_name

where env.env_code = 'prod' and env.tenant_id =app_env_mult_node.suite_code;

SELECT
    m.module_name,
    m.suite_code
FROM (
    SELECT
        module_name,
        suite_code,
        COUNT(DISTINCT suite_code) OVER (PARTITION BY module_name) AS env_count
    FROM app_deploy_info
    WHERE suite_code IN ('pd-prod', 'bs-prod', 'tcloud-prod')
) m
WHERE m.env_count > 1
    AND NOT EXISTS (
        SELECT 1
        FROM zeus_config_instance_assemble zcia
        INNER JOIN zeus_app za ON zcia.app_id = za.id
        INNER JOIN zeus_env ze ON zcia.env_id = ze.id
        WHERE za.name = m.module_name
          AND ze.tenant_id = m.suite_code
          AND ze.env_code = 'prod'
    );




# 查询有多个产线环境的应用
select app_env_node.`module_name`,
       GROUP_CONCAT(DISTINCT app_env_node.suite_code) AS deployed_environments,
       COUNT(DISTINCT app_env_node.suite_code)        AS prod_environment_coun
from app_deploy_info app_env_node
where   app_env_node.suite_code IN ('pd-prod', 'bs-prod', 'tcloud-prod')
GROUP BY app_env_node.`module_name`
HAVING
    prod_environment_coun
     > 1;

SELECT
    module_name,
    suite_code
FROM app_deploy_info
WHERE module_name IN (
    SELECT module_name
    FROM app_deploy_info
    GROUP BY module_name
    HAVING COUNT(DISTINCT suite_code) > 1
)
GROUP BY module_name, suite_code;







-- 查询在不不止一个产线环境发布过配置的应用
SELECT
    app.`name`,
    GROUP_CONCAT(DISTINCT env.tenant_id) AS deployed_environments,
    COUNT(DISTINCT env.tenant_id) AS prod_environment_count
FROM
    zeus_config_instance_assemble instance_assemble
JOIN
    zeus_app app ON instance_assemble.app_id = app.id
JOIN
    zeus_env env ON instance_assemble.env_id = env.id
JOIN
    app_deploy_info app_env_node
    ON app.name = app_env_node.module_name
    AND env.tenant_id = app_env_node.suite_code
WHERE
    env.env_code = 'prod'
    AND env.tenant_id IN ('pd-prod', 'bs-prod', 'tcloud-prod')
GROUP BY
    app.`name`
HAVING
    prod_environment_count > 1;

# 查询有多个产线环境的应用
select app_env_node.`module_name`,
       GROUP_CONCAT(DISTINCT app_env_node.suite_code) AS deployed_environments,
       COUNT(DISTINCT app_env_node.suite_code)        AS prod_environment_coun
from app_deploy_info app_env_node
where   app_env_node.suite_code IN ('pd-prod', 'bs-prod', 'tcloud-prod')
GROUP BY app_env_node.`module_name`
HAVING
    prod_environment_coun
     > 1;

# 查询有多个产线环境的应用,查询应用每个环境有没有在宙斯发过配置
    SELECT
    DISTINCT
    app_env.module_name,
    app_env.suite_code,
    IF(zeus_conf.id IS NULL, '未发布', '已发布') AS config_status
FROM (
    SELECT
        module_name,
        suite_code
    FROM app_deploy_info
    WHERE suite_code IN ('pd-prod', 'bs-prod', 'tcloud-prod')
    AND module_name IN (
        SELECT module_name
        FROM app_deploy_info
        WHERE suite_code IN ('pd-prod', 'bs-prod', 'tcloud-prod')
        GROUP BY module_name
        HAVING COUNT(DISTINCT suite_code) > 1
    )
) app_env
LEFT JOIN (
    SELECT
        za.name AS module_name,
        ze.tenant_id AS suite_code,
        zcia.id
    FROM zeus_config_instance_assemble zcia
    INNER JOIN zeus_app za ON zcia.app_id = za.id
    INNER JOIN zeus_env ze ON zcia.env_id = ze.id
    WHERE ze.env_code = 'prod'
) zeus_conf
ON app_env.module_name = zeus_conf.module_name
AND app_env.suite_code = zeus_conf.suite_code
order by  config_status,app_env.module_name;


# 查询只有 bs-prod的应用
select distinct adi.module_name
from app_deploy_info adi
where adi.suite_code = 'bs-prod'
  AND adi.module_name NOT IN ('cgi-gateway', 'howbuy-fund-server')
  and adi.module_name NOT IN (select distinct adi.module_name
from app_deploy_info adi
where adi.suite_code = 'tcloud-prod'
  AND adi.module_name NOT IN ('cgi-gateway', 'howbuy-fund-server'));

# 查询只有 tcloud-prod的应用
    select distinct adi.module_name
from app_deploy_info adi
where adi.suite_code = 'tcloud-prod'
  AND adi.module_name NOT IN ('cgi-gateway', 'howbuy-fund-server')
  and adi.module_name NOT IN (select distinct adi.module_name
from app_deploy_info adi
where adi.suite_code = 'bs-prod'
  AND adi.module_name NOT IN ('cgi-gateway', 'howbuy-fund-server'));

# 查询同时有 bs-prod,tcloud-prod的应用
    select distinct adi.module_name
from app_deploy_info adi
where adi.suite_code = 'tcloud-prod'
  AND adi.module_name NOT IN ('cgi-gateway', 'howbuy-fund-server')
  and adi.module_name  IN (select distinct adi.module_name
from app_deploy_info adi
where adi.suite_code = 'bs-prod'
  AND adi.module_name NOT IN ('cgi-gateway', 'howbuy-fund-server'));

#
# cgi-gateway,tcloud-prod,已发布
# cgi-gateway,bs-prod,已发布
# howbuy-fund-server,tcloud-prod,已发布
# howbuy-fund-server,bs-prod,已发布




# 查询产线描述符的值
select 1001 AS env_id,
       ztv.tag_id,
       ztv.et_val,
       ztv.et_desc,
       ztv.create_user,
       ztv.create_time,
       ztv.update_user,
       ztv.update_time,
       ztv.stamp
from zeus_tag_val ztv
         INNER JOIN zeus_env zenv ON ztv.env_id = zenv.id
WHERE zenv.tenant_id = 'bs-prod';

# 查询不包括 cgi-gateway,howbuy-fund-server的所有配置
select instance.app_id,
       instance.version,
       1001 AS env_id,
       instance.effect_env_id,
       instance.`key`,
       instance.value,
       instance.`desc`,
       instance.is_env_tag,
       instance.tag_id,
       instance.old_tag_id,
       instance.prefix,
       instance.old_prefix,
       instance.group_name,
       instance.status,
       instance.assemble_val,
       instance.create_user,
       instance.create_time,
       instance.update_user,
       instance.update_time
from zeus_config_instance instance
         INNER JOIN zeus_app zapp ON instance.app_id = zapp.id
         INNER JOIN zeus_env zenv ON instance.env_id = zenv.id
WHERE zenv.tenant_id = 'bs-prod'
  AND zapp.name NOT IN ('cgi-gateway', 'howbuy-fund-server');


select assemble_instance.app_id,
       1001 AS env_id,
       assemble_instance.`key`,
       assemble_instance.value,
       assemble_instance.is_env_tag,
       assemble_instance.tag_id,
       assemble_instance.prefix,
       assemble_instance.group_name,
       assemble_instance.create_user,
       assemble_instance.create_time,
       assemble_instance.update_user,
       assemble_instance.update_time
from zeus_config_instance_assemble assemble_instance
         JOIN
     zeus_app zapp ON assemble_instance.app_id = zapp.id
         JOIN
     zeus_env zenv ON assemble_instance.env_id = zenv.id
WHERE zenv.tenant_id = 'bs-prod'
  AND zapp.name NOT IN ('cgi-gateway', 'howbuy-fund-server');













