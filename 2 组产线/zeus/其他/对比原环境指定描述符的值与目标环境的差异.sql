-- 使用会话变量定义环境参数
SET @source_tenant = 'wgq-zb'; -- 源环境租户
SET @target_tenant = 'chain-zb';
-- 目标环境租户
# 创建临时表 -- 对比的描述符ID集合
DROP TABLE IF EXISTS compare_tagIds;
-- 创建临时表
CREATE TABLE compare_tagIds
(
    tag_id varchar(255)
);
-- 录入指定 tag_id
INSERT INTO compare_tagIds (tag_id)
SELECT DISTINCT zci.tag_id
FROM zeus_config_instance zci
         INNER JOIN zeus_env zenv ON zci.env_id = zenv.id
         INNER JOIN zeus_app zapp ON zapp.id = zci.app_id
         INNER JOIN app_info_devops aid ON aid.app_name = zapp.name
WHERE zenv.tenant_id = 'tcloud-prod'
  AND zci.tag_id IS NOT NULL
  AND zci.is_env_tag = 1
  AND zapp.name = 'howbuy-fund-server'
  AND zci.version = aid.last_archived;

-- 查询差异
SELECT
    COALESCE(s.tag_id, t.tag_id) AS tag_id,
    CASE
        WHEN t.tag_id IS NULL THEN '仅源环境存在'
        WHEN s.tag_id IS NULL THEN '仅目标环境存在'
        WHEN s.et_val != t.et_val THEN '值不一致'
        ELSE '一致'
    END AS 差异类型,
    s.et_val AS 源环境值,
    t.et_val AS 目标环境值,
    s.et_desc AS 源描述,
    t.et_desc AS 目标描述
FROM (
    -- 源环境数据
    SELECT ztv.*
    FROM zeus_tag_val ztv
    INNER JOIN zeus_env ze ON ztv.env_id = ze.id
    WHERE ze.tenant_id = @source_tenant
    and ztv.tag_id IN (SELECT * from compare_tagIds)
) s
LEFT JOIN (
    -- 目标环境数据
    SELECT ztv.*
    FROM zeus_tag_val ztv
    INNER JOIN zeus_env ze ON ztv.env_id = ze.id
    WHERE ze.tenant_id = @target_tenant
    and ztv.tag_id IN (SELECT * from compare_tagIds)
) t ON s.tag_id = t.tag_id

UNION ALL

SELECT
    t.tag_id,
    '仅目标环境存在' AS 差异类型,
    NULL AS 源环境值,
    t.et_val AS 目标环境值,
    NULL AS 源描述,
    t.et_desc AS 目标描述
FROM (
    SELECT ztv.*
    FROM zeus_tag_val ztv
    INNER JOIN zeus_env ze ON ztv.env_id = ze.id
    WHERE ze.tenant_id = @target_tenant
        and ztv.tag_id IN (SELECT * from compare_tagIds)
) t
LEFT JOIN (
    SELECT ztv.*
    FROM zeus_tag_val ztv
    INNER JOIN zeus_env ze ON ztv.env_id = ze.id
    WHERE ze.tenant_id = @source_tenant
        and ztv.tag_id IN (SELECT * from compare_tagIds)
) s ON t.tag_id = s.tag_id
WHERE s.tag_id IS NULL;