# 使用 EC消息的哪些是顺序消息
select distinct `key` as "topicName",REPLACE(JSON_EXTRACT(`value`, '$.groupName'), '"', '') AS groupName from (select distinct app.name, env.env_name, instance.`key`,instance.value
from zeus_config_instance instance
         left join zeus_app app on instance.app_id = app.id
         left join zeus_env env on instance.env_id = env.id
where JSON_VALID(value)
  and JSON_EXTRACT(value, '$.order') is not null
  and group_name="msgConfig"
and env.env_code ='prod'
order by env.env_name) result
where result.env_name not in ("浦东内网生产环境","唐镇验证");

# 使用 EC消息的
select distinct app.name,  env.env_name, instance.`key`,instance.value
from zeus_config_instance instance
         left join zeus_app app on instance.app_id = app.id
         left join zeus_env env on instance.env_id = env.id
where JSON_VALID(value)
    and instance.`key`="queue_das_fps_rhb_fund"
and env.env_code ='prod'
order by env.env_name;


# 使用 原生消息的
select distinct app.name,  env.env_name, instance.`key`,instance.value
from zeus_config_instance instance
         left join zeus_app app on instance.app_id = app.id
         left join zeus_env env on instance.env_id = env.id
where value like "%mainmqns-%"
and env.env_code ='prod'
order by env.env_name;

select distinct app.name,env.env_name,instance.version, instance.`key` as topic,instance.`value`
from zeus_config_instance instance
         left join zeus_app app on instance.app_id = app.id
         left join zeus_env env on instance.env_id = env.id
join app_info_devops aid on aid.app_name=app.name and aid.last_archived=instance.version
where JSON_VALID(value)
and env.env_code ='prod'
and instance.group_name ='msgConfig'
and instance.`key` in ('AMS_FOF_GUZHI_JJDM_TASK',
'AMS_FOF_GUZHI_KHDM_TASK',
'ams_send_scan_file_email_task',
'APP_FUND_INVEST_SYNC',
'CMS_RELATION_IMPORT',
'HOWBUY_ES_TO_GRAYSCAL_MESSAGE_KEY',
'HOWBUY_FUND_NAV_UPDATED_FINISHED',
'howbuy-data-ants_stopTopic',
'howbuy-data-ants_unlockQueue',
'LINK_DATA_HANDLE_4',
'LINK_DATA_HANDLE_5',
'LINK_DATA_HANDLE_6',
'LINK_DATA_HANDLE_7',
'LINK_DATA_HANDLE_8',
'LINK_DATA_HANDLE_FINISH_4',
'LINK_DATA_HANDLE_FINISH_5',
'LINK_DATA_HANDLE_FINISH_6',
'LINK_DATA_HANDLE_FINISH_7',
'LINK_DATA_HANDLE_FINISH_8',
'queue_composite_zsjy_analyse',
'queue_das_fps_fund',
'queue_das_fps_fund_rhb',
'queue_das_fps_fund_rqjhb',
'queue_das_fps_fund_rqjzdhc',
'queue_das_fps_high_cpdy',
'queue_das_fps_portfolio',
'queue_reload_fund_cache',
'queue_zh_fpc_fhjzcp',
'redis_simu_bjjz_flush',
'simu_fixed_lsjz_zl',
'sm_init_product_msg_flush',
'sm_yxs_audition_flush_task',
'WEB_QUEUE_CLZHZJZHGZ',
'WEB_QUEUE_RELOAD_DATA')
and env.env_name not in ("浦东内网生产环境","唐镇验证","虹口内网生产环境","外高桥生产环境");

# 使用 EC消息的
select distinct app.name,  env.env_name, instance.`key`,instance.value,JSON_EXTRACT(instance_server.value, '$.urls')
from zeus_config_instance instance
         left join zeus_app app on instance.app_id = app.id
         left join zeus_env env on instance.env_id = env.id
         left join zeus_config_instance instance_server on JSON_EXTRACT(instance.value, '$.msgServer')=instance_server.`key`
where JSON_VALID(instance.value)
    and instance.`key` in ('fpc_das_fof_reptile_ams',
'queue_das_ams_zdyzhcp_hd',
'queue_das_crm',
'queue_das_crm_jjjz',
'queue_das_fof',
'queue_das_fps_high',
'queue_das_fps_high_ams',
'queue_das_fps_rhb_fund',
'queue_das_tms_asset',
'queue_das_tms_asset_jjjz',
'queue_das_tp'
 )
and env.env_code ='prod'
and env.env_name not in ("浦东内网生产环境","唐镇验证","虹口内网生产环境","外高桥生产环境")
order by env.env_name;

select distinct `key` as "topic",REPLACE(JSON_EXTRACT(`value`, '$.groupName'), '"', '') AS groupName ,
                IF(JSON_EXTRACT(value, '$.order') is not null, "true",null) as "order"

                from (select distinct app.name, env.env_name, instance.`key`,instance.value
from zeus_config_instance instance
         left join zeus_app app on instance.app_id = app.id
         left join zeus_env env on instance.env_id = env.id
where JSON_VALID(value)
  and group_name="msgConfig"
and env.env_code ='prod'

order by env.env_name) result
where result.env_name not in("浦东内网生产环境","唐镇验证","虹口内网生产环境","外高桥生产环境");

