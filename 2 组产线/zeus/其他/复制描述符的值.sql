
#  '旧tagid'
select * from zeus_tag where second_code like "%CRM%" and first_code=5;

#  '新tagid'
select * from zeus_tag where second_code like "%CRM%" and first_code=35;

# 查询新tagid的测试环境值 删掉
select env.env_name,tag.* from zeus_tag_val tag join zeus_env env on env.id = tag.env_id and env.tenant_id != 'prod' where tag_id = '新tagid' and env.env_code!='prod';
select tag.* from zeus_tag_val tag join zeus_env env on env.id = tag.env_id and env.tenant_id != 'prod' where tag_id = '新tagid' and env.env_code!='prod';

# 查询旧tagid的测试环境值
select env.env_name,tag.* from zeus_tag_val tag join zeus_env env on env.id = tag.env_id and env.tenant_id != 'prod' where tag_id = '旧tagid' and env.env_code!='prod';

# 复制旧 tagid 的值到新 tagid
insert into zeus_tag_val(tag_id,env_id,et_val)
select '新tagid',tag.env_id,tag.et_val from zeus_tag_val tag join zeus_env env on env.id = tag.env_id and env.tenant_id != 'prod' where tag.tag_id = '旧tagid' and env.env_code!='prod';



select * from zeus_tag where second_code like "%CRM%" and first_code=5;



