# 发布指定应用版本链路
select distinct * from (
    select distinct aid.app_name,aid.last_archived,adi.suite_code from app_info_devops aid
left join zeus_app azpp  on aid.app_name = azpp.name
left join app_deploy_info adi on aid.app_name = adi.module_name
where adi.suite_code is not null and aid.last_archived is not null
union all
select distinct aid.app_name,aid.last_deployed,adi.suite_code from app_info_devops aid
left join zeus_app azpp  on aid.app_name = azpp.name
left join app_deploy_info adi on aid.app_name = adi.module_name
where adi.suite_code is not null and aid.last_deployed is not null
              ) all_data
order by all_data.app_name,all_data.suite_code;