# 查看有哪些config包含Redis3 -> 10.12.60.50:26379
SELECT d.name,
       c.env_name,
       c.tenant_id,
       b.version,
       b.`key`,
       IF(JSON_EXTRACT(b.value, '$.needLocalCache') = true, 'true', 'false') as needLocal,
       b.value,
       d.id                                                                  as app_id,
       b.env_id,
       b.tag_id
FROM sdk_config_replace_app_deploy_info a
         JOIN zeus_env c ON a.tenant_id = c.tenant_id
         JOIN zeus_app d ON a.app_name = d.name AND a.snapshot_num = '20240518T5'
         join zeus_config_instance b
              ON b.app_id = d.id AND c.id = b.env_id AND a.version = b.version
                  AND JSON_VALID(b.value)
                  AND JSON_EXTRACT(b.value, '$.addrList') LIKE '%mymaster-01%';


# 查看有key使用了受影响的 config
select distinct config_instance.name,
                c_instance.`key`,
                c_instance.value,
                config_instance.`key`  as config_key,
                config_instance.value  as config_value,
                config_instance.tag_id as config_tag_id
from zeus_config_instance c_instance
         join (SELECT d.name,
       c.env_name,
       c.tenant_id,
       b.version,
       b.`key`,
       IF(JSON_EXTRACT(b.value, '$.needLocalCache') = true, 'true', 'false') as needLocal,
       b.value,
       d.id                                                                  as app_id,
       b.env_id,
       b.tag_id
FROM sdk_config_replace_app_deploy_info a
         JOIN zeus_env c ON a.tenant_id = c.tenant_id
         JOIN zeus_app d ON a.app_name = d.name AND a.snapshot_num = '20240518T5'
         join zeus_config_instance b
              ON b.app_id = d.id AND c.id = b.env_id AND a.version = b.version
                  AND JSON_VALID(b.value)
                  AND JSON_EXTRACT(b.value, '$.addrList') LIKE '%mymaster-01%'
                           ) config_instance
              on JSON_EXTRACT(c_instance.value, '$.config') = config_instance.`key`
                  and   JSON_VALID(c_instance.value)
                  and c_instance.app_id = config_instance.app_id and config_instance.env_id = c_instance.env_id
                  and c_instance.version = config_instance.version;



select * from sdk_config_replace_app_key_mapping where  snapshot_num like "%rollback%" order by  snapshot_num,app_name;

# 改后，有哪些 key使用了改后的 key

select distinct config_instance.name,
                c_instance.version,
                config_instance.tenant_id,
                c_instance.`key`,
                config_instance.`key`  as config_key
from zeus_config_instance c_instance
         join (SELECT d.name,
       c.env_name,
       c.tenant_id,
       b.version,
       b.`key`,
       IF(JSON_EXTRACT(b.value, '$.needLocalCache') = true, 'true', 'false') as needLocal,
       b.value,
       d.id                                                                  as app_id,
       b.env_id,
       b.tag_id
FROM sdk_config_replace_app_deploy_info a
         JOIN zeus_env c ON a.tenant_id = c.tenant_id
         JOIN zeus_app d ON a.app_name = d.name AND a.snapshot_num = '20240518T6'
         join zeus_config_instance b
              ON b.app_id = d.id AND c.id = b.env_id AND a.version = b.version
                  AND JSON_VALID(b.value)
                  AND JSON_EXTRACT(b.value, '$.addrList') LIKE '%10.12.60.50:26379%'
                           ) config_instance
              on JSON_EXTRACT(c_instance.value, '$.config') = config_instance.`key`
                  and   JSON_VALID(c_instance.value)
                  and c_instance.app_id = config_instance.app_id and config_instance.env_id = c_instance.env_id
                  and c_instance.version = config_instance.version;



select branch.*
from zeus_app_branch branch
         JOIN zeus_app d ON d.id = branch.app_id
         join sdk_config_replace_app_deploy_info deploy_info
              on deploy_info.version = branch.iteration_number and d.name = deploy_info.app_name
where deploy_info.snapshot_num = '20240518T7-addConfig' and branch.status is null;






select deploy_info.app_name, deploy_info.version, branch.status
from sdk_config_replace_app_deploy_info deploy_info
         join zeus_app_branch branch on deploy_info.version = branch.iteration_number and branch.status != 1
         JOIN zeus_app d ON d.id = branch.app_id
where deploy_info.snapshot_num = '20240518T5-addConfig'
  and deploy_info.app_name in
      ('cgi-simu-container', 'asset-center-remote', 'asset-batch-center-remote', 'high-batch-center-remote','cms-simu','doctor','high-order-trade-remote','product-center-remote')


SELECT t6.*
FROM (
    SELECT * FROM sdk_config_replace_app_deploy_info WHERE snapshot_num = '20240518T6-addConfig-1'
    UNION ALL
    SELECT * FROM sdk_config_replace_app_deploy_info WHERE snapshot_num = '20240518T6-addConfig-2'
) t6
LEFT JOIN (
    SELECT * FROM sdk_config_replace_app_deploy_info WHERE snapshot_num = '20240518T7-addConfig'
) t7 ON t6.app_name = t7.app_name -- 假设id是主键，用来关联两个子查询
WHERE t7.app_name IS NULL;










select branch.*
from zeus_app_branch branch
         JOIN zeus_app d ON d.id = branch.app_id
         join sdk_config_replace_app_deploy_info deploy_info
              on deploy_info.version = branch.iteration_number and d.name = deploy_info.app_name
where deploy_info.snapshot_num = '20240518T7-addConfig' and branch.status is null;

select distinct branch.*
from zeus_app_branch branch
         JOIN zeus_app d ON d.id = branch.app_id
         join sdk_config_replace_app_deploy_info deploy_info
              on deploy_info.version = branch.iteration_number and d.name = deploy_info.app_name
where deploy_info.snapshot_num = '20240518T6-addConfig-1' and branch.status is null;

select distinct branch.*
from zeus_app_branch branch
         JOIN zeus_app d ON d.id = branch.app_id
         join sdk_config_replace_app_deploy_info deploy_info
              on deploy_info.version = branch.iteration_number and d.name = deploy_info.app_name
where deploy_info.snapshot_num = '20240518T6-addConfig-2' and branch.status is null;


select distinct branch.*
from zeus_app_branch branch
         JOIN zeus_app d ON d.id = branch.app_id
         join sdk_config_replace_app_deploy_info deploy_info
              on deploy_info.version = branch.iteration_number and d.name = deploy_info.app_name
where deploy_info.snapshot_num = '20240518T5-addConfig' and branch.status is null;

select  branch.*
from zeus_app_branch branch
         JOIN zeus_app d ON d.id = branch.app_id
where iteration_number='4.3.88' and d.name='elasticsearch-center-remote';



select * from zeus_app_branch where app_id = 363 and iteration_number='1.1.16';
select * from zeus_master_config where app_id = 854 ;


select * from sdk_config_replace_app_deploy_info where app_name='cgi-ehowbuy-container' and snapshot_num='20240518T5-addConfig';
select * from sdk_config_replace_app_deploy_info where snapshot_num='20240518T6-addConfig-2';

INSERT INTO sdk_config_replace_app_deploy_info (app_name, version, tenant_id, snapshot_num, type)
VALUES ('otc-dataservice-server', '20240520', 'prod', '20240518T7', 'replaceConfig');

INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value, snapshot_num)
VALUES ('upgrade', 'otc-dataservice-server', 'otc_data_cache_conf', 'MYMASTR3_REDIS_R', '20240518T7');

INSERT INTO sdk_config_replace_app_deploy_info (app_name, version, tenant_id, snapshot_num, type)
VALUES ('otc-dataservice-server', '20240520', 'prod', '20240518T7-rollback', 'replaceConfig');

INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value, snapshot_num)
VALUES ('upgrade', 'otc-dataservice-server', 'MYMASTR3_REDIS_R', 'otc_data_cache_conf', '20240518T7-rollback');


INSERT INTO sdk_config_replace_app_deploy_info (app_name, version, tenant_id, snapshot_num, type)
VALUES ('otc-direct-pre-server', '6.3.4', 'prod', '20240518T7', 'replaceConfig');
INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value, snapshot_num)
VALUES ('upgrade', 'otc-direct-pre-server', 'MYMASTR3_REDIS_R', 'otc_data_cache_conf', '20240518T7');

INSERT INTO sdk_config_replace_app_deploy_info (app_name, version, tenant_id, snapshot_num, type)
VALUES ('otc-direct-pre-server', '6.3.4', 'prod', '20240518T7-rollback', 'replaceConfig');
INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value, snapshot_num)
VALUES ('upgrade', 'otc-direct-pre-server', 'otc_data_cache_conf', 'MYMASTR3_REDIS_R', '20240518T7-rollback');





INSERT INTO sdk_config_replace_app_deploy_info (app_name, version, tenant_id, snapshot_num, type)
VALUES ('otc-dataservice-server', '20240520', 'prod', '20240518T2', 'replaceConfig');

INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value, snapshot_num)
VALUES ('upgrade', 'about', '10.12.60.83:6379', '10.12.110.228:6379', '20240518T2');

INSERT INTO sdk_config_replace_app_deploy_info (app_name, version, tenant_id, snapshot_num, type)
VALUES ('otc-dataservice-server', '20240520', 'prod', '20240518T2-rollback', 'replaceConfig');

INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value, snapshot_num)
VALUES ('upgrade', 'about', '10.12.110.228:6379', '10.12.60.83:6379', '20240518T2-rollback');



INSERT INTO sdk_config_replace_app_deploy_info (app_name, version, tenant_id, snapshot_num, type)
VALUES ('otc-direct-pre-server', '6.3.4', 'prod', '20240518T2', 'replaceConfig');

INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value, snapshot_num)
VALUES ('upgrade', 'about', '10.12.60.83:6379', '10.12.110.228:6379', '20240518T2');

INSERT INTO sdk_config_replace_app_deploy_info (app_name, version, tenant_id, snapshot_num, type)
VALUES ('otc-direct-pre-server', '6.3.4', 'prod', '20240518T2-rollback', 'replaceConfig');

INSERT INTO sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value, snapshot_num)
VALUES ('upgrade', 'about', '10.12.110.228:6379', '10.12.60.83:6379', '20240518T2-rollback');



select distinct app_name, version, tenant_id
from sdk_config_replace_app_deploy_info
where snapshot_num  in ('20240518T7','20240518T7-1')
  and app_name in ('elasticsearch-center-remote','pension-order-remote');

select *
from sdk_config_replace_app_deploy_info
where snapshot_num  in ('20240518T7')
  and app_name in ('elasticsearch-center-remote');


select distinct app_name, version, tenant_id
from sdk_config_replace_app_deploy_info
where snapshot_num  in ('20240518T7-1');

select *
from sdk_config_replace_app_key_mapping
where snapshot_num  in ('20240518T3');

select *
from sdk_config_replace_app_deploy_info
where snapshot_num  like "%-1%";

select *
from sdk_config_replace_app_deploy_info
where snapshot_num  in ('20240518T3');

select *
from sdk_config_replace_app_deploy_info
where app_name in ('doctor');


SELECT t6.*
FROM (
    SELECT * FROM sdk_config_replace_app_deploy_info WHERE snapshot_num = '20240518T7-1-rollback'
) t6
LEFT JOIN (
    SELECT * FROM sdk_config_replace_app_deploy_info WHERE snapshot_num = '20240518T7-1'
) t7 ON t6.app_name = t7.app_name
WHERE t7.app_name IS NULL;

INSERT INTO zeus_nacos.sdk_config_replace_app_deploy_info (app_name, version, tenant_id, snapshot_num, type) VALUES ('pension-order-remote', '4.4.74', 'bs-zb', '20240518T7-1-rollback', 'replaceConfig');
INSERT INTO zeus_nacos.sdk_config_replace_app_deploy_info (app_name, version, tenant_id, snapshot_num, type) VALUES ('elasticsearch-center-remote', '4.3.88', 'bs-zb', '20240518T7-1-rollback', 'replaceConfig');


INSERT INTO zeus_nacos.sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value, snapshot_num)
VALUES ('upgrade', 'elasticsearch-center-remote', 'MYMASTR3_REDIS_R', 'TRADE_REDIS_R', '20240518T7-1-rollback');
INSERT INTO zeus_nacos.sdk_config_replace_app_key_mapping (type, app_name, source_value, target_value, snapshot_num)
VALUES ('upgrade', 'pension-order-remote', 'MYMASTR3_REDIS_R', 'TRADE_REDIS_R', '20240518T7-1-rollback');


select * from zeus_master_config where app_id=355;

select * from sdk_config_replace_app_deploy_info where app_name='coop-promotion-remote'





-- 改动后的影响
SELECT * FROM zeus_master_config where `key`='MYMASTR3_REDIS_R';
SELECT * FROM zeus_master_config where `key`='MYMASTR2_REDIS_R';

SELECT * FROM zeus_master_config where `key`='PRODUCT_BASIC_KEY_REDIS6';

SELECT app.name,config.* FROM zeus_master_config config
         join zeus_app app on config.app_id=app.id
         where `key`='TRADE_REDIS_R6';


select * from zeus_app_branch where app_id=882 and iteration_number='20240520';


select * from zeus_config_instance_assemble;

select * from zeus_tag_val where et_val like "%mymaster@10.12.60.53:26379&10.12.60.54:26379&10.12.60.55:26379%";


select * from zeus_config_instance where   JSON_VALID(value)
                  AND `key` like "syncCache%"  and JSON_EXTRACT(value, '$.type') !='topic';


select * from zeus_app where id=302;