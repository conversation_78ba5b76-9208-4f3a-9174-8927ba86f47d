select zab.name,ze.env_name, b.value,b.group_name
from zeus_config_instance b
    join zeus_nacos.zeus_app zab on b.app_id = zab.id
join zeus_nacos.zeus_env ze on b.env_id = ze.id and ze.env_code="prod"
where b.group_name = "localCacheGroup"
and b.version="release-20241011-smcache";


select zab.name, b.value,b.group_name
from zeus_config_instance b
    join zeus_nacos.zeus_app zab on b.app_id = zab.id
where b.group_name = "localCacheGroup"
and b.version="release-20241011-smcache";