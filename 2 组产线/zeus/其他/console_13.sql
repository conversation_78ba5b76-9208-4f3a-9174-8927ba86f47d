# 查询指定topic的 msgServer是否正确
select *
from (select distinct instance.*,
                      JSON_EXTRACT(instance_server.value, '$.urls') as urls,
                      JSON_EXTRACT(instance_server.value, '$.type')
      from (select distinct app.name,
                            instance.app_id,
                            instance.version,
                            env.env_name,
                            instance.`key`,
                            instance.value,
                            JSON_EXTRACT(instance.value, '$.msgServer') as msgSever,
                            env.id                                      as env_id
            from zeus_config_instance instance
                     left join zeus_app app on instance.app_id = app.id
                     left join zeus_env env on instance.env_id = env.id
                     join (select app_name,IFNULL(last_deployed,last_archived) onLineVersion from app_info_devops) aid on aid.app_name = app.name and aid.onLineVersion = instance.version
            where  instance.group_name='msgConfig'
              and JSON_VALID(instance.value)
              and env.env_code = 'prod'
              and env.env_name not in ("浦东内网生产环境", "唐镇验证", "虹口内网生产环境", "外高桥生产环境")
            order by env.env_name) instance
               left join zeus_config_instance instance_server
                         on instance.msgSever = instance_server.`key` and instance.env_id = instance_server.env_id and
                            instance.app_id = instance_server.app_id
                             and instance.version = instance_server.version
      where  JSON_VALID(instance_server.value)
       and instance_server.group_name='msgServers'
      order by instance.name, instance.`key`) instance
where  instance.urls like '%canalmqns-1.inner.howbuy.com:9876;canalmqns-2.inner.howbuy.com:9876%'