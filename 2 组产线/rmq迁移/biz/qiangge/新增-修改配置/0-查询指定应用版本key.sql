#所有相关应用版本环境key
SELECT DISTINCT
    app.name,
    instance.app_id,
    instance.version,
    env.env_name,
    env.id AS env_id,
    instance.`key`,
    replace(JSON_EXTRACT(instance.value,"$.msgServer"),'"','')
FROM zeus_config_instance instance
    LEFT JOIN zeus_app app ON instance.app_id = app.id
    LEFT JOIN zeus_env env ON instance.env_id = env.id
    JOIN (SELECT
    app_name,
    IFNULL(last_deployed, last_archived) AS onLineVersion
FROM app_info_devops) aid ON aid.app_name = app.name
        AND aid.onLineVersion = instance.version
    join rmq_migrate_batch_app_topic rmbat on instance.`key`=rmbat.topic_name
WHERE
    JSON_VALID(instance.value)
    AND env.env_code = 'prod'
    AND env.env_name NOT IN ("浦东内网生产环境", "唐镇验证", "虹口内网生产环境", "外高桥生产环境")
ORDER BY app.name;


# 查询需要修改的应用版本环境key
# 创建表
drop table if exists rmq_migrate_batch_instance_key;
create table rmq_migrate_batch_instance_key
SELECT
	all_config.*,
	REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') urls
FROM
	zeus_config_instance instance
	JOIN (
		SELECT DISTINCT
			app.NAME,
			instance.app_id,
			instance.version,
			env.env_name,
			env.id AS env_id,
			instance.`key`,
			instance.id,
			REPLACE(JSON_EXTRACT(instance.VALUE, "$.msgServer"), '"', '') msgServer
		FROM
			zeus_config_instance instance
			LEFT JOIN zeus_app app ON instance.app_id = app.id
			LEFT JOIN zeus_env env ON instance.env_id = env.id
			JOIN (SELECT app_name, IFNULL(last_deployed, last_archived) AS onLineVersion FROM app_info_devops) aid ON aid.app_name = app.NAME
			AND aid.onLineVersion = instance.version
			JOIN rmq_migrate_batch_app_topic rmbat ON instance.`key` = rmbat.topic_name
		WHERE
			JSON_VALID(instance.VALUE)
			AND env.env_code = 'prod'
			AND env.env_name NOT IN ("浦东内网生产环境", "唐镇验证", "虹口内网生产环境", "外高桥生产环境")
		ORDER BY
	app.NAME) all_config ON instance.app_id = all_config.app_id
	AND instance.version = all_config.version
	AND instance.env_id = all_config.env_id
	AND instance.`key` = all_config.msgServer
	where
	 REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%10.11.54.84%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%10.11.54.158%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%rmq1-server.inner.howbuy.com%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%rmq2-server.inner.howbuy.com%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%rmq3-server.inner.howbuy.com%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%rmq4-server.inner.howbuy.com%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%mainmqns-1.inner.howbuy.com%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%mainmqns-2.inner.howbuy.com%";




# 查询需要发布和校验的的应用版本环境
SELECT
	distinct all_config.name as '应用',all_config.version as '版本',all_config.tenant_id as '环境'
FROM
	zeus_config_instance instance
	JOIN (
		SELECT DISTINCT
			app.NAME,
			instance.app_id,
			instance.version,
			env.tenant_id,
			env.id AS env_id,
			instance.`key`,
			REPLACE(JSON_EXTRACT(instance.VALUE, "$.msgServer"), '"', '') msgServer
		FROM
			zeus_config_instance instance
			LEFT JOIN zeus_app app ON instance.app_id = app.id
			LEFT JOIN zeus_env env ON instance.env_id = env.id
			JOIN (SELECT app_name, IFNULL(last_deployed, last_archived) AS onLineVersion FROM app_info_devops) aid ON aid.app_name = app.NAME
			AND aid.onLineVersion = instance.version
			JOIN rmq_migrate_batch_app_topic rmbat ON instance.`key` = rmbat.topic_name
		WHERE
			JSON_VALID(instance.VALUE)
			AND env.env_code = 'prod'
			AND env.env_name NOT IN ("浦东内网生产环境", "唐镇验证", "虹口内网生产环境", "外高桥生产环境")
		ORDER BY
	app.NAME) all_config ON instance.app_id = all_config.app_id
	AND instance.version = all_config.version
	AND instance.env_id = all_config.env_id
	AND instance.`key` = all_config.msgServer
	where
	 REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%10.11.54.84%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%10.11.54.158%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%rmq1-server.inner.howbuy.com%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%rmq2-server.inner.howbuy.com%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%rmq3-server.inner.howbuy.com%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%rmq4-server.inner.howbuy.com%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%mainmqns-1.inner.howbuy.com%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%mainmqns-2.inner.howbuy.com%";


# 查询需要nacos校验的的应用版本环境
drop table if exists rmq_migrate_batch_instance;
drop table if exists rmq_migrate_batch_app_env_version;
create table rmq_migrate_batch_app_env_version
SELECT
	distinct all_config.name,all_config.app_id ,all_config.version ,all_config.tenant_id,all_config.env_id
FROM
	zeus_config_instance instance
	JOIN (
		SELECT DISTINCT
			app.NAME,
			instance.app_id,
			instance.version,
			env.tenant_id,
			env.id AS env_id,
			instance.`key`,
			REPLACE(JSON_EXTRACT(instance.VALUE, "$.msgServer"), '"', '') msgServer
		FROM
			zeus_config_instance instance
			LEFT JOIN zeus_app app ON instance.app_id = app.id
			LEFT JOIN zeus_env env ON instance.env_id = env.id
			JOIN (SELECT app_name, IFNULL(last_deployed, last_archived) AS onLineVersion FROM app_info_devops) aid ON aid.app_name = app.NAME
			AND aid.onLineVersion = instance.version
			JOIN rmq_migrate_batch_app_topic rmbat ON instance.`key` = rmbat.topic_name
		WHERE
			JSON_VALID(instance.VALUE)
			AND env.env_code = 'prod'
			AND env.env_name NOT IN ("浦东内网生产环境", "唐镇验证", "虹口内网生产环境", "外高桥生产环境")
		ORDER BY
	app.NAME) all_config ON instance.app_id = all_config.app_id
	AND instance.version = all_config.version
	AND instance.env_id = all_config.env_id
	AND instance.`key` = all_config.msgServer
	where
	 REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%10.11.54.84%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%10.11.54.158%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%rmq1-server.inner.howbuy.com%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%rmq2-server.inner.howbuy.com%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%rmq3-server.inner.howbuy.com%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%rmq4-server.inner.howbuy.com%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%mainmqns-1.inner.howbuy.com%"
         or REPLACE(JSON_EXTRACT(instance.VALUE, "$.urls"), '"', '') like "%mainmqns-2.inner.howbuy.com%";