
set @rmq_migrate_batch='rmq-迁移001';
# 新增config
INSERT INTO zeus_config (app_id, version, `key`, init_value, `desc`, is_env_tag, tag_id, old_tag_id,
                                        prefix, old_prefix, group_name, status, create_user, create_time, update_user,
                                        update_time)
select app_id,
       version,
       'howbuyUnionRmqServer',
       '',
       @rmq_migrate_batch,
       '1',
       1614593481615,
       'true|1614593481615|',
       '1',
       null,
       'msgServers',
       'N',
       'howbuyscm',
       now(),
       null,
       now()
from (select distinct app_id,version from  rmq_migrate_batch_app_env_version ) rmbaev;



# 新增config_instance
INSERT INTO zeus_config_instance (app_id, version, env_id, effect_env_id, `key`, value, `desc`,
                                                 is_env_tag, tag_id, old_tag_id, prefix, old_prefix, group_name, status,
                                                 assemble_val, create_user, create_time, update_user, update_time)
select app_id,
       version,
       env_id,
       0,
       'howbuyUnionRmqServer',
       '{"urls":"mq1ns-1.inner.howbuy.com:9876;mq2ns-1.inner.howbuy.com:9876;mq3ns-1.inner.howbuy.com:9876","type":"RocketMQ"}',
       @rmq_migrate_batch,
       '1',
       1614593481615,
       '1614593481615',
       '1',
       '1',
       'msgServers',
       'N',
       null,
       'yang.zhan',
       now(),
       'yang.zhan',
       now()
from rmq_migrate_batch_app_env_version;



set @rmq_migrate_batch='rmq-迁移001';
# 修改config_instance  msgServer=howbuyUnionRmqServer
UPDATE zeus_config_instance zci
JOIN rmq_migrate_batch_instance_key tui ON zci.id = tui.id
SET zci.`value` = JSON_SET(zci.`value`, '$.msgServer', 'howbuyUnionRmqServer') , `desc`=@rmq_migrate_batch
where JSON_VALID(zci.`value`);
select count(1) from zeus_config_instance where `desc`=@rmq_migrate_batch;

# zeus_config_instance_backup_rmq 与check 改前值与改后值
SELECT 'config_instance'                            AS '表名',
       backup.id                                    AS '记录ID',
       backup.app_id                                AS '应用ID',
       backup.`key`                                 AS '配置键',
       JSON_EXTRACT(backup.`value`, '$.msgServer')  AS '修改前msgServer值',
       JSON_EXTRACT(current.`value`, '$.msgServer') AS '修改后msgServer值',
       CASE
           WHEN JSON_EXTRACT(current.`value`, '$.msgServer') = 'howbuyUnionRmqServer' THEN '修改成功'
           ELSE '修改失败'
           END                                      AS '修改状态'
FROM zeus_config_instance_backup_rmq backup
         JOIN
     zeus_config_instance current ON backup.id = current.id
WHERE backup.id IN (SELECT id FROM rmq_migrate_batch_instance_key);

set @rmq_migrate_batch='rmq-迁移001';
# 修改config_instance_assmble
update zeus_config_instance_assemble  assemble
join (select zcia.id
             from zeus_config_instance_assemble zcia
                      join rmq_migrate_batch_instance_key rmbik on
                 zcia.app_id = rmbik.app_id and zcia.env_id = rmbik.env_id and zcia.`key` = rmbik.`key`) batch ON batch.id = assemble.id
set `value` =JSON_SET(`value`, '$.msgServer', 'howbuyUnionRmqServer')
where JSON_VALID(`value`);
select count(1) from zeus_config_instance where `desc`=@rmq_migrate_batch;

# zeus_config_instance_assemble_backup_rmq 与check 改前值与改后值
SELECT 'config_instance_assemble'                   AS '表名',
       backup.id                                    AS '记录ID',
       backup.app_id                                AS '应用ID',
       backup.`key`                                 AS '配置键',
       JSON_EXTRACT(backup.`value`, '$.msgServer')  AS '修改前msgServer值',
       JSON_EXTRACT(current.`value`, '$.msgServer') AS '修改后msgServer值',
       CASE
           WHEN JSON_EXTRACT(current.`value`, '$.msgServer') = 'howbuyUnionRmqServer' THEN '修改成功'
           ELSE '修改失败'
           END                                      AS '修改状态'
FROM zeus_config_instance_assemble_backup_rmq backup
         JOIN
     zeus_config_instance_assemble current ON backup.id = current.id
WHERE backup.id IN (SELECT zcia.id
                    FROM zeus_config_instance_assemble zcia
                             JOIN rmq_migrate_batch_instance_key rmbik ON
                        zcia.app_id = rmbik.app_id AND zcia.env_id = rmbik.env_id AND zcia.`key` = rmbik.`key`);

# check汇总
SELECT '配置修改统计'                                                                               AS '统计项',
       (SELECT COUNT(*) FROM rmq_migrate_batch_instance_key)                                        AS '需要修改的config_instance记录数',
       (SELECT COUNT(*)
        FROM zeus_config_instance
        WHERE id IN (SELECT id FROM rmq_migrate_batch_instance_key)
          AND JSON_EXTRACT(`value`, '$.msgServer') = 'howbuyUnionRmqServer')                        AS '成功修改的config_instance记录数',
       (SELECT COUNT(*)
        FROM zeus_config_instance_assemble zcia
                 JOIN rmq_migrate_batch_instance_key rmbik ON
            zcia.app_id = rmbik.app_id AND zcia.env_id = rmbik.env_id AND zcia.`key` =
                                                                          rmbik.`key`)              AS '需要修改的assemble记录数',
       (SELECT COUNT(*)
        FROM zeus_config_instance_assemble zcia
                 JOIN rmq_migrate_batch_instance_key rmbik ON
            zcia.app_id = rmbik.app_id AND zcia.env_id = rmbik.env_id AND zcia.`key` = rmbik.`key`
        WHERE JSON_EXTRACT(zcia.`value`, '$.msgServer') = 'howbuyUnionRmqServer')                   AS '成功修改的assemble记录数';

# 新增master
INSERT INTO zeus_master_config (app_id, `key`, init_value, `desc`, is_env_tag, tag_id, prefix,
                                               group_name, create_user, create_time, update_user, update_time)
select app_id,
       'howbuyUnionRmqServer',
       '',
       '',
       '1',
       1614593481615,
       '1',
       'msgServers',
       'synchronous',
       now(),
       null,
       now()
from rmq_migrate_batch_app_env_version;

# 修改master最新归档时间
UPDATE `zeus_push_pull_record`
SET `push_time` = now()
WHERE `id` in (select id, app_id, iteration_number
               from zeus_push_pull_record
               where app_id in (select distinct app_id from rmq_migrate_batch_instance_key)
                 and iteration_number = 'zeus_master')



