# 如果备份表存在，先将原有备份表重命名
SET @current_time = DATE_FORMAT(NOW(), '%Y%m%d%H%i%s');

DROP PROCEDURE IF EXISTS rename_backup_tables;
DELIMITER //
CREATE PROCEDURE rename_backup_tables()
BEGIN
    DECLARE table_exists INT;
    
    # 检查并重命名config_instance备份表
    SELECT COUNT(*) INTO table_exists FROM information_schema.tables 
    WHERE table_schema = DATABASE() AND table_name = 'zeus_config_instance_backup_rmq';
    
    IF table_exists > 0 THEN
        SET @rename_sql1 = CONCAT('RENAME TABLE zeus_config_instance_backup_rmq TO zeus_config_instance_backup_rmq_', @current_time);
        PREPARE stmt1 FROM @rename_sql1;
        EXECUTE stmt1;
        DEALLOCATE PREPARE stmt1;
    END IF;
    
    # 检查并重命名config_instance_assemble备份表
    SELECT COUNT(*) INTO table_exists FROM information_schema.tables 
    WHERE table_schema = DATABASE() AND table_name = 'zeus_config_instance_assemble_backup_rmq';
    
    IF table_exists > 0 THEN
        SET @rename_sql2 = CONCAT('RENAME TABLE zeus_config_instance_assemble_backup_rmq TO zeus_config_instance_assemble_backup_rmq_', @current_time);
        PREPARE stmt2 FROM @rename_sql2;
        EXECUTE stmt2;
        DEALLOCATE PREPARE stmt2;
    END IF;
END //
DELIMITER ;

CALL rename_backup_tables();
DROP PROCEDURE IF EXISTS rename_backup_tables;


# 备份要修改的config_instance数据
DROP TABLE IF EXISTS zeus_config_instance_backup_rmq;
CREATE TABLE zeus_config_instance_backup_rmq LIKE zeus_config_instance;
INSERT INTO zeus_config_instance_backup_rmq
SELECT * FROM zeus_config_instance
WHERE id IN (SELECT id FROM rmq_migrate_batch_instance_key);

# 备份要修改的config_instance_assemble数据
DROP TABLE IF EXISTS zeus_config_instance_assemble_backup_rmq;
CREATE TABLE zeus_config_instance_assemble_backup_rmq LIKE zeus_config_instance_assemble;
INSERT INTO zeus_config_instance_assemble_backup_rmq
SELECT zcia.*
FROM zeus_config_instance_assemble zcia
JOIN rmq_migrate_batch_instance_key rmbik ON
    zcia.app_id = rmbik.app_id AND zcia.env_id = rmbik.env_id AND zcia.`key` = rmbik.`key`;
