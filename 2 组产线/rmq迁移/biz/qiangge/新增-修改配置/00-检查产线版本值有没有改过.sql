# 查询需要发布和校验的的应用版本环境zeus_config_instance_assemble与zeus_config_instance 值是否一致
SELECT
    app.name AS '应用名称',
    instance.version AS '版本',
    env.env_name AS '环境名称',
    instance.`key` AS '配置键',
    instance.`value` AS 'instance值',
    assemble.`value` AS 'assemble值',
    IF(instance.`value` <=> assemble.`value`, '一致', '不一致') AS '比对结果'
FROM
    rmq_migrate_batch_app_env_version batch
JOIN
    zeus_config_instance instance ON
        instance.app_id = batch.app_id AND
        instance.env_id = batch.env_id AND
        instance.version = batch.version
JOIN
    zeus_config_instance_assemble assemble ON
        instance.app_id = assemble.app_id AND
        instance.env_id = assemble.env_id AND
        instance.`key` = assemble.`key`
JOIN
    zeus_app app ON instance.app_id = app.id
JOIN
    zeus_env env ON instance.env_id = env.id
where instance.`value` != assemble.`value`
ORDER BY
    app.name, env.env_name, instance.`key`;



