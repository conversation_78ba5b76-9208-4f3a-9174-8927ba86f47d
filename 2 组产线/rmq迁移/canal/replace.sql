
# 修改描述符的值的语句语句
update zeus_tag_val
set et_val='{"type":"RocketMQ","urls":"mq1ns-1.inner.howbuy.com:9876;mq2ns-1.inner.howbuy.com:9876;mq3ns-1.inner.howbuy.com:9876"}'
where id in (1858197461613,
             2005271805094852035,
             1557408591588);

# 改使用了sdk的配置的值 开始
# 修改instance的key和值
update zeus_config_instance
set value='{"type":"RocketMQ","urls":"mq1ns-1.inner.howbuy.com:9876;mq2ns-1.inner.howbuy.com:9876;mq3ns-1.inner.howbuy.com:9876"}'
where id in (
11480781,
11886113,
11960801,
11980259,
11982118,
11480705,
11886383,
11960901,
11980796,
11982470,
11981333,
11982822
    );

# 修改需要修改的assemble的key和值
update zeus_config_instance_assemble
set value='{"type":"RocketMQ","urls":"mq1ns-1.inner.howbuy.com:9876;mq2ns-1.inner.howbuy.com:9876;mq3ns-1.inner.howbuy.com:9876"}'
where id in (
606037,
610458,
612071,
612270,
632276,
632349,
634132,
634205,
673636,
675900,
675903);
# 改使用了sdk的配置的值 结束



# 改没使用sdk的配置的值 开始

# 修改instance的key和值
update zeus_config_instance
set value='{"type":"RocketMQ","urls":"mq1ns-1.inner.howbuy.com:9876;mq2ns-1.inner.howbuy.com:9876;mq3ns-1.inner.howbuy.com:9876"}'
where id in (
11977362,11977555
);

# 修改instance的key和值
update zeus_config_instance
set value='mq1ns-1.inner.howbuy.com:9876;mq2ns-1.inner.howbuy.com:9876;mq3ns-1.inner.howbuy.com:9876'
where id in (10990202,11977368,11977560);


# 修改需要修改的assemble的key和值
update zeus_config_instance_assemble
set value='{"type":"RocketMQ","urls":"mq1ns-1.inner.howbuy.com:9876;mq2ns-1.inner.howbuy.com:9876;mq3ns-1.inner.howbuy.com:9876"}'
where id in (411511,439905);

# 修改需要修改的assemble的key和值
update zeus_config_instance_assemble
set value='mq1ns-1.inner.howbuy.com:9876;mq2ns-1.inner.howbuy.com:9876;mq3ns-1.inner.howbuy.com:9876'
where id in (297460,213065,213293);
# 改没使用sdk的配置的值 结束