

-- 修改 instance表，指定应用、版本、环境、key

SELECT instance.*
FROM zeus_config_instance instance
         INNER JOIN zeus_app app
                    ON instance.app_id = app.id
         INNER JOIN zeus_env env
                    ON instance.env_id = env.id
                        AND env.env_code = 'prod'
                        AND env.env_name NOT IN ('浦东内网生产环境', '唐镇验证', '虹口内网生产环境', '外高桥生产环境')
WHERE instance.`key` in ('syncCache|cgiCache')
  AND JSON_VALID(instance.value)
  AND EXISTS (SELECT 1
              FROM app_info_devops aid
              WHERE aid.app_name = app.name
                AND instance.version = COALESCE(aid.last_deployed, aid.last_archived));


-- 修改 instance_assemble表，指定应用、环境、key
SELECT instance_assemble.*
FROM zeus_config_instance_assemble instance_assemble
         INNER JOIN zeus_env env
                    ON instance_assemble.env_id = env.id
                        AND env.env_code = 'prod'
                        AND env.env_name NOT IN ('浦东内网生产环境', '唐镇验证', '虹口内网生产环境', '外高桥生产环境')
WHERE instance_assemble.`key` in ('syncCache|cgiCache')
  AND JSON_VALID(instance_assemble.value);

-- 查询指定topic的 msgServer是否正确   HOWBUY_UNION_MQ_SERVER
select *
from (select distinct instance.*,
                      JSON_EXTRACT(instance_server.value, '$.urls') as urls,
                      JSON_EXTRACT(instance_server.value, '$.type')
      from (select distinct app.name,
                            instance.app_id,
                            instance.version,
                            env.env_name,
                            instance.`key`,
                            instance.value,
                            JSON_EXTRACT(instance.value, '$.msgServer') as msgSever,
                            env.id                                      as env_id
            from zeus_config_instance instance
                     left join zeus_app app on instance.app_id = app.id
                     left join zeus_env env on instance.env_id = env.id
                     join (select app_name,IFNULL(last_deployed,last_archived) onLineVersion from app_info_devops) aid on aid.app_name = app.name and aid.onLineVersion = instance.version
            where  JSON_VALID(instance.value)
              and env.env_code = 'prod'
              and env.env_name not in ("浦东内网生产环境", "唐镇验证", "虹口内网生产环境", "外高桥生产环境")
            order by env.env_name) instance
               left join zeus_config_instance instance_server
                         on instance.msgSever = instance_server.`key` and instance.env_id = instance_server.env_id and
                            instance.app_id = instance_server.app_id
                             and instance.version = instance_server.version
      order by instance.name, instance.`key`) instance
where  instance.urls not like "%canalmqns%"
  and instance.urls not like "%10.11.54.66%"
  and instance.urls not like "%10.11.54.121%";
-- 批量发布


-- 宙斯配置对比



