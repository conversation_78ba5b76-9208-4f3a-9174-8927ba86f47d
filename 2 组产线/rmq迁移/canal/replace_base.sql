# 查询需要描述符的值的ID的语句
select    CONCAT(s1.code, '_', zt.second_code, '_', s3.code) AS '描述符',ztv.*
from zeus_tag_val ztv
         INNER JOIN zeus_tag zt ON ztv.tag_id = zt.id
         INNER JOIN zeus_tag_segment_dict s1 ON zt.first_code = s1.id
         INNER JOIN zeus_tag_segment_dict s3 ON zt.third_code = s3.id
         INNER JOIN zeus_env env on ztv.env_id = env.id and
                              env.env_name in ("wgq-zb", "唐镇生产环境", "tcloud-prod")
where tag_id in (select td.tag_id
                 from (select distinct app.name, env_name, instance.`key`, value, tag_id, '是' as '描述符'
                       from zeus_config_instance instance
                                join zeus_app app on instance.app_id = app.id
                                join zeus_env env on instance.env_id = env.id and
                                                     env.env_name in
                                                     ("wgq-zb", "唐镇生产环境", "tcloud-prod")
                                join (select app_name, IFNULL(last_deployed, last_archived) onLineVersion
                                      from app_info_devops) aid
                                     on aid.app_name = app.name and aid.onLineVersion = instance.version
                       where (instance.value like "%canalmqns%"
                           or instance.value like "%rmq1-canal%"
                           or instance.value like "%10.11.54.66%"
                           or instance.value like "%10.11.54.121%")
                         and instance.is_env_tag = 1) td);

# 修改描述符的值的语句语句
# update zeus_tag_val
# set et_val='{"type":"RocketMQ","urls":"mq1ns-1.inner.howbuy.com:9876;mq2ns-1.inner.howbuy.com:9876;mq3ns-1.inner.howbuy.com:9876"}'
# where id in (1858197461613,
#              2005271805094852035,
#              1557408591588);

# 改使用了sdk的配置的值 开始
# 修改需要修改的instance的key和值
select distinct instance.id,
                             app.name,
                             instance.version,
                             env_name,
                             instance.`key`,
                             value,
                             tag_id,
                             '是' as '描述符'
             from zeus_config_instance instance
                      join zeus_app app on instance.app_id = app.id
                      join zeus_env env
                           on instance.env_id = env.id and env.env_name in ("wgq-zb", "唐镇生产环境", "tcloud-prod")
                      join (select app_name, IFNULL(last_deployed, last_archived) onLineVersion
                            from app_info_devops) aid
                           on aid.app_name = app.name and aid.onLineVersion = instance.version
             where (instance.value like "%canalmqns%"
                 or instance.value like "%rmq1-canal%"
                 or instance.value like "%10.11.54.66%"
                 or instance.value like "%10.11.54.121%")
               and instance.is_env_tag = 1;

# 修改instance的key和值
# update zeus_config_instance
# set value='{"type":"RocketMQ","urls":"mq1ns-1.inner.howbuy.com:9876;mq2ns-1.inner.howbuy.com:9876;mq3ns-1.inner.howbuy.com:9876"}'
# where id in (
# 11480781,
# 11886113,
# 11960801,
# 11980259,
# 11982118,
# 11480705,
# 11886383,
# 11960901,
# 11980796,
# 11982470,
# 11981333,
# 11982822,
#     );

# 需要修改的assemble的key和值
    select distinct assemble.id,
                             app.name,
                             env_name,
                             assemble.`key`,
                             value,
                             tag_id,
                             '是' as '描述符'
             from zeus_config_instance_assemble assemble
                      join zeus_app app on assemble.app_id = app.id
                      join zeus_env env
                           on assemble.env_id = env.id and env.env_name in ("wgq-zb", "唐镇生产环境", "tcloud-prod")
             where (assemble.value like "%canalmqns%"
                 or assemble.value like "%rmq1-canal%"
                 or assemble.value like "%10.11.54.66%"
                 or assemble.value like "%10.11.54.121%")
               and assemble.is_env_tag = 1;

# 整理ID
# 修改需要修改的assemble的key和值
# update zeus_config_instance_assemble
# set value='{"type":"RocketMQ","urls":"mq1ns-1.inner.howbuy.com:9876;mq2ns-1.inner.howbuy.com:9876;mq3ns-1.inner.howbuy.com:9876"}'
# where id in (
# 606037,
# 610458,
# 612071,
# 612270,
# 632276,
# 632349,
# 634132,
# 634205,
# 673636,
# 675900,
# 675903);
# 改使用了sdk的配置的值 结束



# 改没使用sdk的配置的值 开始
select distinct instance.id,
                             app.name,
                             instance.version,
                             env_name,
                             instance.`key`,
                             value,
                             tag_id,
                             '非' as '描述符'
             from zeus_config_instance instance
                      join zeus_app app on instance.app_id = app.id
                      join zeus_env env
                           on instance.env_id = env.id and env.env_name in ("wgq-zb", "唐镇生产环境", "tcloud-prod")
                      join (select app_name, IFNULL(last_deployed, last_archived) onLineVersion
                            from app_info_devops) aid
                           on aid.app_name = app.name and aid.onLineVersion = instance.version
             where (instance.value like "%canalmqns%"
                 or instance.value like "%rmq1-canal%"
                 or instance.value like "%10.11.54.66%"
                 or instance.value like "%10.11.54.121%")
               and instance.is_env_tag != 1;

# 修改instance的key和值
# update zeus_config_instance
# set value='{"type":"RocketMQ","urls":"mq1ns-1.inner.howbuy.com:9876;mq2ns-1.inner.howbuy.com:9876;mq3ns-1.inner.howbuy.com:9876"}'
# where id in (
# 11977362,11977555
# );

# 修改instance的key和值
# update zeus_config_instance
# set value='mq1ns-1.inner.howbuy.com:9876;mq2ns-1.inner.howbuy.com:9876;mq3ns-1.inner.howbuy.com:9876'
# where id in (10990202,11977368,11977560);



# 需要修改的assemble的key和值
select distinct assemble.id,
                             app.name,
                             env_name,
                             assemble.`key`,
                             value,
                             tag_id,
                             '是' as '描述符'
             from zeus_config_instance_assemble assemble
                      join zeus_app app on assemble.app_id = app.id
                      join zeus_env env
                           on assemble.env_id = env.id and env.env_name in ("wgq-zb", "唐镇生产环境", "tcloud-prod")
             where (assemble.value like "%canalmqns%"
                 or assemble.value like "%rmq1-canal%"
                 or assemble.value like "%10.11.54.66%"
                 or assemble.value like "%10.11.54.121%")
               and assemble.is_env_tag != 1;

# 整理ID
# 修改需要修改的assemble的key和值
# update zeus_config_instance_assemble
# set value='{"type":"RocketMQ","urls":"mq1ns-1.inner.howbuy.com:9876;mq2ns-1.inner.howbuy.com:9876;mq3ns-1.inner.howbuy.com:9876"}'
# where id in (411511,439905);

# 修改需要修改的assemble的key和值
# update zeus_config_instance_assemble
# set value='mq1ns-1.inner.howbuy.com:9876;mq2ns-1.inner.howbuy.com:9876;mq3ns-1.inner.howbuy.com:9876'
# where id in (297460,213065,213293);
# 改没使用sdk的配置的值 结束