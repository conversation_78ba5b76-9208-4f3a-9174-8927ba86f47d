# 批量发布
select distinct * from (
select distinct app.name as '应用名',
                instance.version as '版本',
                env.tenant_id as '环境'
from zeus_config_instance instance
         left join zeus_app app on instance.app_id = app.id
         left join zeus_env env on instance.env_id = env.id
         join (select app_name, IFNULL(last_deployed, last_archived) onLineVersion from app_info_devops) aid
              on aid.app_name = app.name and instance.version=aid.onLineVersion
where instance.`key` in (select distinct topicName
                         from rmq_migrate_pqc_canal)
  and JSON_VALID(instance.value)
  and env.env_code = 'prod'
  and env.tenant_id  in ("wgq-zb", "bs-prod", "tcloud-prod")
union ALL
select distinct app.name as '应用名',
                instance.version as '版本',
                env.tenant_id as '环境'
from zeus_config_instance instance
         left join zeus_app app on instance.app_id = app.id
         left join zeus_env env on instance.env_id = env.id
         join (select app_name, IFNULL(last_deployed, last_archived) onLineVersion from app_info_devops) aid
               on aid.app_name = app.name and instance.version=aid.onLineVersion
where (instance.value like "%canalmqns%" or  instance.value like "%rmq1-canal%" or instance.value like "%10.11.54.66%" or instance.value like "%10.11.54.121%")
  and not JSON_VALID(instance.value)
  and env.env_code = 'prod'
  and env.tenant_id in ("wgq-zb", "bs-prod", "tcloud-prod")
order by '环境'
)tc;



# 批量对比nacos app_zeus_version_env.json
select distinct * from (
select distinct app.name,
                instance.app_id,
                aid.onLineVersion as version,
                env.tenant_id,
                env.id as env_id
from zeus_config_instance instance
         left join zeus_app app on instance.app_id = app.id
         left join zeus_env env on instance.env_id = env.id
         join (select app_name, IFNULL(last_deployed, last_archived) onLineVersion from app_info_devops) aid
              on aid.app_name = app.name and instance.version=aid.onLineVersion
where instance.`key` in (select distinct topicName
                         from rmq_migrate_pqc_canal)
  and JSON_VALID(instance.value)
  and env.env_code = 'prod'
  and env.tenant_id  in ("wgq-zb", "bs-prod", "tcloud-prod")
union ALL
select distinct app.name,
                instance.app_id,
                 aid.onLineVersion as version,
                 env.tenant_id,
                env.id as env_id
from zeus_config_instance instance
         left join zeus_app app on instance.app_id = app.id
         left join zeus_env env on instance.env_id = env.id
         join (select app_name, IFNULL(last_deployed, last_archived) onLineVersion from app_info_devops) aid
             on aid.app_name = app.name and instance.version=aid.onLineVersion
where (instance.value like "%canalmqns%" or  instance.value like "%rmq1-canal%" or instance.value like "%10.11.54.66%" or instance.value like "%10.11.54.121%")
  and not JSON_VALID(instance.value)
  and env.env_code = 'prod'
  and env.tenant_id in ("wgq-zb", "bs-prod", "tcloud-prod")
order by name,tenant_id
)tc;









